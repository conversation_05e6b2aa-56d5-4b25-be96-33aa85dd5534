import React, { useState } from 'react';
import { FaComments, FaTimes, FaStar, FaHeart, FaThumbsUp, FaLightbulb, FaBug } from 'react-icons/fa';

const FeedbackModal = ({ isOpen, onClose }) => {
  const [feedbackType, setFeedbackType] = useState('general');
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [feedbackText, setFeedbackText] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const feedbackTypes = [
    { id: 'general', label: 'General Feedback', icon: <FaComments />, color: 'blue' },
    { id: 'bug', label: 'Report Bug', icon: <FaBug />, color: 'red' },
    { id: 'feature', label: 'Feature Request', icon: <FaLightbulb />, color: 'yellow' },
    { id: 'compliment', label: 'Compliment', icon: <FaHeart />, color: 'pink' }
  ];

  const experienceEmojis = [
    { emoji: '😞', label: 'Poor', value: 1 },
    { emoji: '😐', label: 'Fair', value: 2 },
    { emoji: '😊', label: 'Good', value: 3 },
    { emoji: '😍', label: 'Great', value: 4 },
    { emoji: '🤩', label: 'Excellent', value: 5 }
  ];

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Mock API call - replace with actual implementation
      const feedbackData = {
        type: feedbackType,
        rating,
        feedback: feedbackText,
        email: email || null,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };

      console.log('Submitting feedback:', feedbackData);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setSubmitted(true);
      
      // Auto close after success
      setTimeout(() => {
        handleClose();
      }, 2000);
      
    } catch (error) {
      console.error('Error submitting feedback:', error);
      alert('Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFeedbackType('general');
    setRating(0);
    setHoverRating(0);
    setFeedbackText('');
    setEmail('');
    setSubmitted(false);
    setIsSubmitting(false);
    onClose();
  };

  const renderStars = (currentRating) => {
    return [...Array(5)].map((_, index) => (
      <button
        key={index}
        type="button"
        className={`text-2xl transition-colors duration-200 ${
          index < (hoverRating || currentRating) 
            ? 'text-yellow-400 hover:text-yellow-500' 
            : 'text-gray-300 hover:text-yellow-400'
        }`}
        onMouseEnter={() => setHoverRating(index + 1)}
        onMouseLeave={() => setHoverRating(0)}
        onClick={() => setRating(index + 1)}
      >
        <FaStar />
      </button>
    ));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm" 
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-gradient-to-r from-amber-600 to-orange-600 text-white p-6 rounded-t-2xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                <FaComments className="text-xl" />
              </div>
              <div>
                <h3 className="text-xl font-bold font-serif-primary">We Value Your Feedback</h3>
                <p className="text-amber-100 text-sm">Help us improve your experience</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-white/80 hover:text-white text-xl p-1 rounded-full hover:bg-white/20 transition-colors"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {submitted ? (
            // Success State
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaThumbsUp className="text-green-600 text-2xl" />
              </div>
              <h4 className="text-xl font-bold text-green-800 mb-2 font-serif-primary">
                Thank You!
              </h4>
              <p className="text-green-700 font-sans">
                Your feedback has been submitted successfully. We appreciate your input!
              </p>
            </div>
          ) : (
            // Feedback Form
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Feedback Type */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  What type of feedback do you have?
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {feedbackTypes.map((type) => (
                    <button
                      key={type.id}
                      type="button"
                      onClick={() => setFeedbackType(type.id)}
                      className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                        feedbackType === type.id
                          ? 'border-amber-500 bg-amber-50 text-amber-700'
                          : 'border-gray-200 hover:border-gray-300 text-gray-600'
                      }`}
                    >
                      <div className="flex flex-col items-center space-y-1">
                        <span className="text-lg">{type.icon}</span>
                        <span className="text-xs font-semibold">{type.label}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Experience Rating */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  How was your experience?
                </label>
                
                {/* Emoji Rating */}
                <div className="flex justify-center space-x-2 mb-4">
                  {experienceEmojis.map((item) => (
                    <button
                      key={item.value}
                      type="button"
                      onClick={() => setRating(item.value)}
                      className={`text-3xl p-2 rounded-full transition-all duration-200 ${
                        rating === item.value
                          ? 'bg-amber-100 scale-110'
                          : 'hover:bg-gray-100 hover:scale-105'
                      }`}
                      title={item.label}
                    >
                      {item.emoji}
                    </button>
                  ))}
                </div>

                {/* Star Rating */}
                <div className="flex justify-center space-x-1">
                  {renderStars(rating)}
                </div>
                
                {rating > 0 && (
                  <p className="text-center text-sm text-gray-600 mt-2">
                    {experienceEmojis.find(e => e.value === rating)?.label} Experience
                  </p>
                )}
              </div>

              {/* Feedback Text */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Tell us more {feedbackType === 'bug' ? 'about the issue' : 
                              feedbackType === 'feature' ? 'about your idea' :
                              feedbackType === 'compliment' ? 'about what you loved' :
                              '(optional)'}
                </label>
                <textarea
                  value={feedbackText}
                  onChange={(e) => setFeedbackText(e.target.value)}
                  placeholder={
                    feedbackType === 'bug' ? 'Please describe the bug you encountered...' :
                    feedbackType === 'feature' ? 'What feature would you like to see?' :
                    feedbackType === 'compliment' ? 'What did we do well?' :
                    'What can we improve?'
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none
                            focus:ring-2 focus:ring-amber-500 focus:border-transparent
                            font-sans text-gray-800"
                  rows="4"
                  required={feedbackType === 'bug'}
                />
              </div>

              {/* Email (Optional) */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Email (optional)
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full p-3 border border-gray-300 rounded-lg
                            focus:ring-2 focus:ring-amber-500 focus:border-transparent
                            font-sans text-gray-800"
                />
                <p className="text-xs text-gray-500 mt-1">
                  We'll only use this to follow up if needed
                </p>
              </div>

              {/* Submit Buttons */}
              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={handleClose}
                  className="flex-1 bg-gray-200 text-gray-800 py-3 rounded-lg font-semibold
                            hover:bg-gray-300 transition-colors duration-300"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting || (!feedbackText && feedbackType === 'bug')}
                  className="flex-1 bg-gradient-to-r from-amber-600 to-orange-600 text-white 
                            py-3 rounded-lg font-semibold hover:from-amber-700 hover:to-orange-700 
                            transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default FeedbackModal;
