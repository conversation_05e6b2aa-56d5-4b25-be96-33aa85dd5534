/* Admin Interface Styles */
.admin-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.admin-navbar {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  padding: 12px 0;
}

.admin-navbar .navbar-brand {
  font-weight: 700;
  font-size: 24px;
  color: white !important;
}

.admin-navbar .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  margin: 0 8px;
  padding: 8px 16px !important;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.admin-navbar .nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white !important;
  transform: translateY(-1px);
}

.admin-home {
  padding: 40px 20px;
}

.admin-header {
  text-align: center;
  margin-bottom: 40px;
}

.admin-title {
  font-size: 36px;
  font-weight: 700;
  color: white;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.admin-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
}

.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.admin-stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.admin-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.admin-stat-card.users::before {
  background: linear-gradient(90deg, #4299e1, #667eea);
}

.admin-stat-card.sellers::before {
  background: linear-gradient(90deg, #48bb78, #38a169);
}

.admin-stat-card.books::before {
  background: linear-gradient(90deg, #ed8936, #dd6b20);
}

.admin-stat-card.orders::before {
  background: linear-gradient(90deg, #9f7aea, #805ad5);
}

.admin-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.admin-stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 16px;
}

.admin-stat-icon.users {
  background: linear-gradient(135deg, #4299e1, #667eea);
  color: white;
}

.admin-stat-icon.sellers {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
}

.admin-stat-icon.books {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
  color: white;
}

.admin-stat-icon.orders {
  background: linear-gradient(135deg, #9f7aea, #805ad5);
  color: white;
}

.admin-stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 4px;
}

.admin-stat-label {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.admin-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

.admin-action-btn {
  padding: 14px 28px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.admin-action-btn.users {
  background: linear-gradient(135deg, #4299e1, #667eea);
}

.admin-action-btn.users:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(66, 153, 225, 0.3);
  color: white;
  text-decoration: none;
}

.admin-action-btn.sellers {
  background: linear-gradient(135deg, #48bb78, #38a169);
}

.admin-action-btn.sellers:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
  color: white;
  text-decoration: none;
}

.admin-action-btn.books {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.admin-action-btn.books:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(237, 137, 54, 0.3);
  color: white;
  text-decoration: none;
}

.admin-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.admin-section-title {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-section-title::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
}

.admin-table th {
  background: #f8fafc;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 2px solid #e2e8f0;
}

.admin-table td {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  color: #2d3748;
}

.admin-table tr:hover {
  background: #f8fafc;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 12px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #2d3748;
}

.user-email {
  font-size: 14px;
  color: #718096;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.active {
  background: #c6f6d5;
  color: #2f855a;
}

.status-badge.inactive {
  background: #fed7d7;
  color: #c53030;
}

.status-badge.pending {
  background: #feebc8;
  color: #c05621;
}

.admin-search {
  margin-bottom: 24px;
  position: relative;
}

.admin-search-input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.2s ease;
}

.admin-search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.admin-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
  font-size: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-title {
    font-size: 28px;
  }
  
  .admin-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .admin-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .admin-action-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .admin-section {
    padding: 20px;
  }
  
  .admin-table {
    font-size: 14px;
  }
  
  .admin-table th,
  .admin-table td {
    padding: 12px 8px;
  }
}

/* Charts and Analytics */
.admin-chart-container {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.admin-chart-title {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 24px;
}

/* Loading and Empty States */
.admin-loading {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}

.admin-empty {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}

.admin-empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}
