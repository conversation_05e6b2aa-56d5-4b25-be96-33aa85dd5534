const mongoose = require('mongoose');
require('./db/config');
const items = require('./db/Seller/Additem');

// Missing books that are in uploads but not in database
const missingBooks = [
  {
    title: "The First Law Trilogy",
    author: "<PERSON>",
    genre: "Fantasy",
    description: "A gritty fantasy trilogy that subverts traditional fantasy tropes.",
    price: "599",
    itemImage: "The First Law Trilogy.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    title: "The Girl with the Dragon Tattoo",
    author: "<PERSON><PERSON><PERSON>",
    genre: "Crime Thriller",
    description: "A gripping crime thriller about a journalist and a hacker investigating a disappearance.",
    price: "379",
    itemImage: "The Girl with the Dragon Tattoo.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    title: "The Hating Game",
    author: "<PERSON>",
    genre: "Contemporary Romance",
    description: "A witty enemies-to-lovers romance set in a corporate office.",
    price: "329",
    itemImage: "The Hating Game.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    title: "The Last Mrs. Parrish",
    author: "Liv Constantine",
    genre: "Psychological Thriller",
    description: "A twisted tale of deception, betrayal, and revenge.",
    price: "349",
    itemImage: "The Last Mrs. Parrish.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    title: "The Left Hand of Darkness",
    author: "Ursula K. Le Guin",
    genre: "Science Fiction",
    description: "A groundbreaking science fiction novel exploring gender and society.",
    price: "359",
    itemImage: "The Left Hand of Darkness.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    title: "The Miracle Morning",
    author: "Hal Elrod",
    genre: "Self-Help",
    description: "Transform your life with morning routines that will change everything.",
    price: "399",
    itemImage: "The Miracle Morning.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    title: "The Nightingale",
    author: "Kristin Hannah",
    genre: "Historical Fiction",
    description: "A powerful story of two sisters in Nazi-occupied France.",
    price: "389",
    itemImage: "The Nightingale.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    title: "The Pillars of the Earth",
    author: "Ken Follett",
    genre: "Historical Fiction",
    description: "An epic tale of ambition, politics, and love set in 12th-century England.",
    price: "499",
    itemImage: "The Pillars of the Earth.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    title: "The Power of Now",
    author: "Eckhart Tolle",
    genre: "Spirituality",
    description: "A guide to spiritual enlightenment and living in the present moment.",
    price: "379",
    itemImage: "The Power of Now.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    title: "The Song of Achilles",
    author: "Madeline Miller",
    genre: "Historical Fiction",
    description: "A beautiful retelling of the Iliad focusing on Achilles and Patroclus.",
    price: "369",
    itemImage: "The Song of Achilles.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    title: "The Tattooist of Auschwitz",
    author: "Heather Morris",
    genre: "Historical Fiction",
    description: "Based on the true story of love and survival in Auschwitz.",
    price: "359",
    itemImage: "The Tattooist of Auschwitz.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    title: "Train to Pakistan",
    author: "Khushwant Singh",
    genre: "Historical Fiction",
    description: "A powerful novel about the partition of India and Pakistan.",
    price: "299",
    itemImage: "Train to Pakistan.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin",
    isTopRecommendation: false
  }
];

async function addMissingBooks() {
  try {
    console.log('📚 Adding missing books to database...');
    
    let addedCount = 0;
    
    for (const book of missingBooks) {
      // Check if book already exists
      const existingBook = await items.findOne({ 
        title: book.title, 
        author: book.author 
      });
      
      if (!existingBook) {
        await items.create(book);
        console.log(`✅ Added: ${book.title} by ${book.author}`);
        addedCount++;
      } else {
        console.log(`⚠️  Already exists: ${book.title}`);
      }
    }
    
    console.log(`\n🎉 Successfully added ${addedCount} new books!`);
    
    // Show final count
    const totalBooks = await items.countDocuments();
    console.log(`📊 Total books in database: ${totalBooks}`);
    
  } catch (error) {
    console.error('❌ Error adding missing books:', error);
  } finally {
    mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

addMissingBooks();
