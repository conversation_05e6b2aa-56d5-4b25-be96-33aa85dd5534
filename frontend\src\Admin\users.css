/**
Book Store User Management Styles
---------------------------------
This file contains styles for the user management/admin user list page.
You can import this CSS in your React components as needed.
*/

/* User Table Styles */
.user-table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(60, 60, 120, 0.08);
}

.user-table th,
.user-table td {
  padding: 1rem;
  text-align: left;
}

.user-table th {
  background: #4f46e5;
  color: #fff;
  font-weight: 600;
  letter-spacing: 0.03em;
}

.user-table tr:nth-child(even) {
  background: #f3f4f6;
}

.user-table tr:hover {
  background: #e0e7ff;
  transition: background 0.2s;
}

/* Action Buttons */
.user-action-btn {
  background: #6366f1;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 0.4rem 1rem;
  margin-right: 0.5rem;
  cursor: pointer;
  font-size: 0.95rem;
  transition: background 0.2s;
}

.user-action-btn.delete {
  background: #ef4444;
}

.user-action-btn:hover {
  background: #4338ca;
}

.user-action-btn.delete:hover {
  background: #b91c1c;
}

/* User List Container */
.user-list-container {
  max-width: 900px;
  margin: 2rem auto;
  padding: 2rem;
  background: #f9fafb;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(60, 60, 120, 0.06);
}

/* Responsive */
@media (max-width: 700px) {
  .user-table, .user-table thead, .user-table tbody, .user-table th, .user-table td, .user-table tr {
    display: block;
  }
  .user-table th {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
  .user-table td {
    position: relative;
    padding-left: 50%;
    border: none;
    border-bottom: 1px solid #e5e7eb;
  }
  .user-table td:before {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 45%;
    white-space: nowrap;
    font-weight: bold;
    color: #6366f1;
    content: attr(data-label);
  }
}

/* Add User Form */
.add-user-form {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
  background: #fff;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(60, 60, 120, 0.06);
}

.add-user-form input,
.add-user-form select {
  flex: 1 1 200px;
  padding: 0.7rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 1rem;
}

.add-user-form button {
  background: #4f46e5;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 0.7rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.add-user-form button:hover {
  background: #3730a3;
}

/* Success/Error Messages */
.user-message {
  margin: 1rem 0;
  padding: 0.8rem 1.2rem;
  border-radius: 4px;
  font-size: 1rem;
}

.user-message.success {
  background: #d1fae5;
  color: #065f46;
}

.user-message.error {
  background: #fee2e2;
  color: #991b1b;
}
*/
.user-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 2rem;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(60, 60, 120, 0.06);
}

.user-table th,
.user-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.user-table th {
  background: #f3f4f6;
  color: #374151;
  font-weight: 600;
}

.user-table tr:last-child td {
  border-bottom: none;
}

.user-table .action-btn {
  background: #4f46e5;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 0.4rem 1rem;
  font-size: 0.95rem;
  cursor: pointer;
  margin-right: 0.5rem;
  transition: background 0.2s;
}

.user-table .action-btn:hover {
  background: #3730a3;
}

.user-table .delete-btn {
  background: #ef4444;
}

.user-table .delete-btn:hover {
  background: #b91c1c;
}
