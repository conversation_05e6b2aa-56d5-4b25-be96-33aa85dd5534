services:
  # Frontend Service
  - type: web
    name: booknest-frontend
    env: static
    buildCommand: cd frontend && npm install && npm run build
    staticPublishPath: frontend/dist
    routes:
      - type: rewrite
        source: /*
        destination: /index.html

  # Backend Service  
  - type: web
    name: booknest-backend
    env: node
    buildCommand: cd Backend && npm install
    startCommand: cd Backend && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
