import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Button, Table, Badge } from 'react-bootstrap';
import { FaUsers, FaStore, FaBook, FaShoppingCart, FaChartBar, FaEye } from 'react-icons/fa';
import { BarChart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import Anavbar from './Anavbar';
import Footer from '../Components/Footer';

// Admin Dashboard for Book Store
function Ahome() {
  const [users, setUsers] = useState([]);
  const [vendors, setVendors] = useState([]);
  const [books, setBooks] = useState([]);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchAdminData();
  }, []);

  const fetchAdminData = async () => {
    try {
      setLoading(true);

      // For demo purposes, create sample admin data
      const sampleUsers = [
        { _id: '1', name: '<PERSON>e', email: '<EMAIL>', joinDate: '2024-01-15', status: 'Active' },
        { _id: '2', name: 'Jane Smith', email: '<EMAIL>', joinDate: '2024-02-20', status: 'Active' },
        { _id: '3', name: 'Mike Johnson', email: '<EMAIL>', joinDate: '2024-03-10', status: 'Inactive' },
        { _id: '4', name: 'Sarah Wilson', email: '<EMAIL>', joinDate: '2024-04-05', status: 'Active' }
      ];

      const sampleVendors = [
        { _id: '1', name: 'BookWorld Store', email: '<EMAIL>', joinDate: '2024-01-10', booksCount: 25, status: 'Active' },
        { _id: '2', name: 'Classic Books Co.', email: '<EMAIL>', joinDate: '2024-02-15', booksCount: 18, status: 'Active' },
        { _id: '3', name: 'Modern Literature', email: '<EMAIL>', joinDate: '2024-03-20', booksCount: 12, status: 'Pending' }
      ];

      const sampleBooks = [
        { _id: '1', title: '1984', author: 'George Orwell', category: 'Fiction', price: 299, seller: 'BookWorld Store' },
        { _id: '2', title: 'Pride and Prejudice', author: 'Jane Austen', category: 'Classic', price: 349, seller: 'Classic Books Co.' },
        { _id: '3', title: 'The Great Gatsby', author: 'F. Scott Fitzgerald', category: 'Classic', price: 279, seller: 'BookWorld Store' },
        { _id: '4', title: 'To Kill a Mockingbird', author: 'Harper Lee', category: 'Fiction', price: 329, seller: 'Modern Literature' }
      ];

      const sampleOrders = [
        { _id: '1', customerName: 'John Doe', bookTitle: '1984', amount: 299, date: '2024-12-20', status: 'Delivered' },
        { _id: '2', customerName: 'Jane Smith', bookTitle: 'Pride and Prejudice', amount: 349, date: '2024-12-22', status: 'Processing' },
        { _id: '3', customerName: 'Mike Johnson', bookTitle: 'The Great Gatsby', amount: 279, date: '2024-12-25', status: 'Shipped' }
      ];

      setUsers(sampleUsers);
      setVendors(sampleVendors);
      setBooks(sampleBooks);
      setOrders(sampleOrders);
    } catch (error) {
      console.error('Error fetching admin data:', error);
    } finally {
      setLoading(false);
    }
  };

  const totalUsers = users.length;
  const totalVendors = vendors.length;
  const totalBooks = books.length;
  const totalOrders = orders.length;
  const totalRevenue = orders.reduce((sum, order) => sum + order.amount, 0);

  const chartData = [
    { name: 'Users', value: totalUsers, fill: '#8b4513' },
    { name: 'Sellers', value: totalVendors, fill: '#2c1810' },
    { name: 'Books', value: totalBooks, fill: '#a0522d' },
    { name: 'Orders', value: totalOrders, fill: '#d2691e' },
  ];

  if (loading) {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #2c1810 0%, #8b4513 100%)' }}>
        <Anavbar />
        <Container className="py-5">
          <div className="text-center text-white">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3">Loading admin dashboard...</p>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #2c1810 0%, #8b4513 100%)' }}>
      <Anavbar />

      <Container className="py-5">
        {/* Header */}
        <Row className="mb-5">
          <Col className="text-center">
            <h1 className="text-white mb-3" style={{ fontFamily: 'Playfair Display, serif', fontSize: '3rem' }}>
              Admin Dashboard
            </h1>
            <p className="text-white-50 fs-5">Manage users, sellers, and monitor BookNest operations</p>
          </Col>
        </Row>

        {/* Stats Cards */}
        <Row className="mb-5">
          <Col md={3} className="mb-4">
            <Card
              className="h-100 shadow-lg text-center cursor-pointer"
              style={{ background: 'rgba(255,255,255,0.95)' }}
              onClick={() => navigate('/users')}
            >
              <Card.Body>
                <FaUsers size={40} style={{ color: '#8b4513' }} className="mb-3" />
                <h3 style={{ color: '#2c1810' }}>{totalUsers}</h3>
                <p className="text-muted mb-0">Total Users</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card
              className="h-100 shadow-lg text-center cursor-pointer"
              style={{ background: 'rgba(255,255,255,0.95)' }}
              onClick={() => navigate('/sellers')}
            >
              <Card.Body>
                <FaStore size={40} style={{ color: '#8b4513' }} className="mb-3" />
                <h3 style={{ color: '#2c1810' }}>{totalVendors}</h3>
                <p className="text-muted mb-0">Total Sellers</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card
              className="h-100 shadow-lg text-center cursor-pointer"
              style={{ background: 'rgba(255,255,255,0.95)' }}
              onClick={() => navigate('/items')}
            >
              <Card.Body>
                <FaBook size={40} style={{ color: '#8b4513' }} className="mb-3" />
                <h3 style={{ color: '#2c1810' }}>{totalBooks}</h3>
                <p className="text-muted mb-0">Total Books</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card
              className="h-100 shadow-lg text-center cursor-pointer"
              style={{ background: 'rgba(255,255,255,0.95)' }}
              onClick={() => navigate('/orders')}
            >
              <Card.Body>
                <FaShoppingCart size={40} style={{ color: '#8b4513' }} className="mb-3" />
                <h3 style={{ color: '#2c1810' }}>{totalOrders}</h3>
                <p className="text-muted mb-0">Total Orders</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Charts and Tables Row */}
        <Row className="mb-5">
          {/* Chart */}
          <Col lg={6} className="mb-4">
            <Card className="shadow-lg h-100" style={{ background: 'rgba(255,255,255,0.95)' }}>
              <Card.Header style={{ background: '#8b4513', color: 'white' }}>
                <h5 className="mb-0 d-flex align-items-center">
                  <FaChartBar className="me-2" />
                  Platform Statistics
                </h5>
              </Card.Header>
              <Card.Body>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={chartData}>
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="value" fill="#8b4513" />
                  </BarChart>
                </ResponsiveContainer>
              </Card.Body>
            </Card>
          </Col>

          {/* Revenue Card */}
          <Col lg={6} className="mb-4">
            <Card className="shadow-lg h-100" style={{ background: 'rgba(255,255,255,0.95)' }}>
              <Card.Header style={{ background: '#8b4513', color: 'white' }}>
                <h5 className="mb-0">Revenue Overview</h5>
              </Card.Header>
              <Card.Body className="d-flex flex-column justify-content-center text-center">
                <h2 style={{ color: '#8b4513', fontSize: '3rem' }}>₹{totalRevenue}</h2>
                <p className="text-muted fs-5">Total Revenue</p>
                <div className="mt-3">
                  <Badge bg="success" className="me-2">+12% this month</Badge>
                  <Badge bg="info">Active Platform</Badge>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
        {/* Recent Activity Tables */}
        <Row>
          {/* Recent Users */}
          <Col lg={6} className="mb-4">
            <Card className="shadow-lg" style={{ background: 'rgba(255,255,255,0.95)' }}>
              <Card.Header style={{ background: '#8b4513', color: 'white' }}>
                <div className="d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">Recent Users</h5>
                  <Button variant="light" size="sm" onClick={() => navigate('/users')}>
                    <FaEye className="me-1" />
                    View All
                  </Button>
                </div>
              </Card.Header>
              <Card.Body>
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.slice(0, 3).map(user => (
                      <tr key={user._id}>
                        <td>{user.name}</td>
                        <td>{user.email}</td>
                        <td>
                          <Badge bg={user.status === 'Active' ? 'success' : 'secondary'}>
                            {user.status}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          </Col>

          {/* Recent Orders */}
          <Col lg={6} className="mb-4">
            <Card className="shadow-lg" style={{ background: 'rgba(255,255,255,0.95)' }}>
              <Card.Header style={{ background: '#8b4513', color: 'white' }}>
                <div className="d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">Recent Orders</h5>
                  <Button variant="light" size="sm" onClick={() => navigate('/orders')}>
                    <FaEye className="me-1" />
                    View All
                  </Button>
                </div>
              </Card.Header>
              <Card.Body>
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Customer</th>
                      <th>Book</th>
                      <th>Amount</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.slice(0, 3).map(order => (
                      <tr key={order._id}>
                        <td>{order.customerName}</td>
                        <td>{order.bookTitle}</td>
                        <td>₹{order.amount}</td>
                        <td>
                          <Badge bg={
                            order.status === 'Delivered' ? 'success' :
                            order.status === 'Processing' ? 'info' : 'warning'
                          }>
                            {order.status}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
      <Footer />
    </div>
  );
}

export default Ahome;
