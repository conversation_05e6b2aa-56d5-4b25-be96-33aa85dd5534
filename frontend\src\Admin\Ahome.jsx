import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Button, Table, Badge } from 'react-bootstrap';
import { FaUsers, FaStore, FaBook, FaShoppingCart, FaChartBar, FaEye } from 'react-icons/fa';
import { BarChart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import Anavbar from './Anavbar';
import Footer from '../Components/Footer';

// Admin Dashboard for Book Store
function Ahome() {
  const [users, setUsers] = useState([]);
  const [vendors, setVendors] = useState([]);
  const [books, setBooks] = useState([]);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchAdminData();
  }, []);

  const fetchAdminData = async () => {
    try {
      setLoading(true);

      // For demo purposes, create sample admin data
      const sampleUsers = [
        { _id: '1', name: '<PERSON>e', email: '<EMAIL>', joinDate: '2024-01-15', status: 'Active' },
        { _id: '2', name: 'Jane Smith', email: '<EMAIL>', joinDate: '2024-02-20', status: 'Active' },
        { _id: '3', name: 'Mike Johnson', email: '<EMAIL>', joinDate: '2024-03-10', status: 'Inactive' },
        { _id: '4', name: 'Sarah Wilson', email: '<EMAIL>', joinDate: '2024-04-05', status: 'Active' }
      ];

      const sampleVendors = [
        { _id: '1', name: 'BookWorld Store', email: '<EMAIL>', joinDate: '2024-01-10', booksCount: 25, status: 'Active' },
        { _id: '2', name: 'Classic Books Co.', email: '<EMAIL>', joinDate: '2024-02-15', booksCount: 18, status: 'Active' },
        { _id: '3', name: 'Modern Literature', email: '<EMAIL>', joinDate: '2024-03-20', booksCount: 12, status: 'Pending' }
      ];

      const sampleBooks = [
        { _id: '1', title: '1984', author: 'George Orwell', category: 'Fiction', price: 299, seller: 'BookWorld Store' },
        { _id: '2', title: 'Pride and Prejudice', author: 'Jane Austen', category: 'Classic', price: 349, seller: 'Classic Books Co.' },
        { _id: '3', title: 'The Great Gatsby', author: 'F. Scott Fitzgerald', category: 'Classic', price: 279, seller: 'BookWorld Store' },
        { _id: '4', title: 'To Kill a Mockingbird', author: 'Harper Lee', category: 'Fiction', price: 329, seller: 'Modern Literature' }
      ];

      const sampleOrders = [
        { _id: '1', customerName: 'John Doe', bookTitle: '1984', amount: 299, date: '2024-12-20', status: 'Delivered' },
        { _id: '2', customerName: 'Jane Smith', bookTitle: 'Pride and Prejudice', amount: 349, date: '2024-12-22', status: 'Processing' },
        { _id: '3', customerName: 'Mike Johnson', bookTitle: 'The Great Gatsby', amount: 279, date: '2024-12-25', status: 'Shipped' }
      ];

      setUsers(sampleUsers);
      setVendors(sampleVendors);
      setBooks(sampleBooks);
      setOrders(sampleOrders);
    } catch (error) {
      console.error('Error fetching admin data:', error);
    } finally {
      setLoading(false);
    }
  };

  const totalUsers = users.length;
  const totalVendors = vendors.length;
  const totalBooks = books.length;
  const totalOrders = orders.length;
  const totalRevenue = orders.reduce((sum, order) => sum + order.amount, 0);

  const chartData = [
    { name: 'Users', value: totalUsers, fill: '#8b4513' },
    { name: 'Sellers', value: totalVendors, fill: '#2c1810' },
    { name: 'Books', value: totalBooks, fill: '#a0522d' },
    { name: 'Orders', value: totalOrders, fill: '#d2691e' },
  ];

  if (loading) {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)' }}>
        <Anavbar />
        <Container className="py-5">
          <div className="text-center">
            <div className="spinner-border text-warning" role="status" style={{ color: '#FF6600' }}>
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3" style={{ color: '#666', fontFamily: 'Segoe UI, sans-serif' }}>Loading admin dashboard...</p>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)' }}>
      <Anavbar />

      <Container className="py-5">
        {/* Modern Header */}
        <Row className="mb-5">
          <Col className="text-center">
            <h1 className="mb-3" style={{
              fontFamily: 'Segoe UI, sans-serif',
              fontSize: '2.5rem',
              color: '#333',
              fontWeight: '600'
            }}>
              🛡️ Admin Control Center
            </h1>
            <p className="fs-5" style={{ color: '#666', fontFamily: 'Segoe UI, sans-serif' }}>
              Monitor platform performance and manage all operations
            </p>
          </Col>
        </Row>

        {/* Modern Interactive Stats Cards */}
        <Row className="mb-5">
          <Col md={3} className="mb-4">
            <Card
              className="h-100 border-0 shadow-sm text-center"
              style={{
                background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out'
              }}
              onClick={() => navigate('/users')}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-8px)';
                e.currentTarget.style.boxShadow = '0 12px 30px rgba(0,123,255,0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0px)';
                e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
              }}
            >
              <Card.Body className="text-white p-4">
                <FaUsers size={48} className="mb-3" style={{ opacity: 0.9 }} />
                <h2 className="mb-2" style={{ fontFamily: 'Segoe UI, sans-serif', fontWeight: '700' }}>{totalUsers}</h2>
                <p className="mb-0" style={{ fontFamily: 'Segoe UI, sans-serif', fontSize: '0.95rem', opacity: 0.9 }}>
                  👥 Total Users
                </p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card
              className="h-100 border-0 shadow-sm text-center"
              style={{
                background: 'linear-gradient(135deg, #28a745 0%, #1e7e34 100%)',
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out'
              }}
              onClick={() => navigate('/sellers')}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-8px)';
                e.currentTarget.style.boxShadow = '0 12px 30px rgba(40,167,69,0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0px)';
                e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
              }}
            >
              <Card.Body className="text-white p-4">
                <FaStore size={48} className="mb-3" style={{ opacity: 0.9 }} />
                <h2 className="mb-2" style={{ fontFamily: 'Segoe UI, sans-serif', fontWeight: '700' }}>{totalVendors}</h2>
                <p className="mb-0" style={{ fontFamily: 'Segoe UI, sans-serif', fontSize: '0.95rem', opacity: 0.9 }}>
                  🏪 Total Sellers
                </p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card
              className="h-100 border-0 shadow-sm text-center"
              style={{
                background: 'linear-gradient(135deg, #FF6600 0%, #FF9900 100%)',
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out'
              }}
              onClick={() => navigate('/items')}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-8px)';
                e.currentTarget.style.boxShadow = '0 12px 30px rgba(255,102,0,0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0px)';
                e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
              }}
            >
              <Card.Body className="text-white p-4">
                <FaBook size={48} className="mb-3" style={{ opacity: 0.9 }} />
                <h2 className="mb-2" style={{ fontFamily: 'Segoe UI, sans-serif', fontWeight: '700' }}>{totalBooks}</h2>
                <p className="mb-0" style={{ fontFamily: 'Segoe UI, sans-serif', fontSize: '0.95rem', opacity: 0.9 }}>
                  📚 Total Books
                </p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card
              className="h-100 border-0 shadow-sm text-center"
              style={{
                background: 'linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%)',
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out'
              }}
              onClick={() => navigate('/orders')}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-8px)';
                e.currentTarget.style.boxShadow = '0 12px 30px rgba(111,66,193,0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0px)';
                e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
              }}
            >
              <Card.Body className="text-white p-4">
                <FaShoppingCart size={48} className="mb-3" style={{ opacity: 0.9 }} />
                <h2 className="mb-2" style={{ fontFamily: 'Segoe UI, sans-serif', fontWeight: '700' }}>{totalOrders}</h2>
                <p className="mb-0" style={{ fontFamily: 'Segoe UI, sans-serif', fontSize: '0.95rem', opacity: 0.9 }}>
                  🛒 Total Orders
                </p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Modern Charts and Revenue Section */}
        <Row className="mb-5">
          {/* Interactive Analytics Chart */}
          <Col lg={6} className="mb-4">
            <Card className="border-0 shadow-sm h-100" style={{ background: '#ffffff' }}>
              <Card.Header style={{
                background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px 8px 0 0'
              }}>
                <h4 className="mb-0 d-flex align-items-center" style={{
                  fontFamily: 'Segoe UI, sans-serif',
                  fontWeight: '600',
                  fontSize: '1.3rem'
                }}>
                  <FaChartBar className="me-2" />
                  📈 Platform Analytics
                </h4>
              </Card.Header>
              <Card.Body style={{ padding: '2rem' }}>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={chartData}>
                    <XAxis
                      dataKey="name"
                      style={{ fontFamily: 'Segoe UI, sans-serif', fontSize: '12px' }}
                    />
                    <YAxis
                      style={{ fontFamily: 'Segoe UI, sans-serif', fontSize: '12px' }}
                    />
                    <Tooltip
                      contentStyle={{
                        background: '#fff',
                        border: '1px solid #ddd',
                        borderRadius: '8px',
                        fontFamily: 'Segoe UI, sans-serif'
                      }}
                    />
                    <Bar dataKey="value" fill="#FF6600" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </Card.Body>
            </Card>
          </Col>

          {/* Enhanced Revenue Card */}
          <Col lg={6} className="mb-4">
            <Card className="border-0 shadow-sm h-100" style={{ background: '#ffffff' }}>
              <Card.Header style={{
                background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px 8px 0 0'
              }}>
                <h4 className="mb-0" style={{
                  fontFamily: 'Segoe UI, sans-serif',
                  fontWeight: '600',
                  fontSize: '1.3rem'
                }}>
                  💰 Revenue Overview
                </h4>
              </Card.Header>
              <Card.Body className="d-flex flex-column justify-content-center text-center" style={{ padding: '2rem' }}>
                <h1 style={{
                  color: '#28a745',
                  fontSize: '3.5rem',
                  fontFamily: 'Segoe UI, sans-serif',
                  fontWeight: '700',
                  marginBottom: '1rem'
                }}>
                  ₹{totalRevenue.toLocaleString()}
                </h1>
                <p style={{
                  color: '#666',
                  fontSize: '1.2rem',
                  fontFamily: 'Segoe UI, sans-serif',
                  marginBottom: '2rem'
                }}>
                  Total Platform Revenue
                </p>
                <div className="d-flex justify-content-center gap-3">
                  <Badge style={{
                    background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                    border: 'none',
                    borderRadius: '15px',
                    padding: '8px 16px',
                    fontFamily: 'Segoe UI, sans-serif',
                    fontSize: '0.9rem'
                  }}>
                    📈 +12% this month
                  </Badge>
                  <Badge style={{
                    background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
                    border: 'none',
                    borderRadius: '15px',
                    padding: '8px 16px',
                    fontFamily: 'Segoe UI, sans-serif',
                    fontSize: '0.9rem'
                  }}>
                    🟢 Platform Active
                  </Badge>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
        {/* Modern Recent Activity Tables */}
        <Row>
          {/* Enhanced Recent Users */}
          <Col lg={6} className="mb-4">
            <Card className="border-0 shadow-sm" style={{ background: '#ffffff' }}>
              <Card.Header style={{
                background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px 8px 0 0'
              }}>
                <div className="d-flex justify-content-between align-items-center">
                  <h5 className="mb-0" style={{
                    fontFamily: 'Segoe UI, sans-serif',
                    fontWeight: '600'
                  }}>
                    👥 Recent Users
                  </h5>
                  <Button
                    size="sm"
                    onClick={() => navigate('/users')}
                    style={{
                      background: 'rgba(255,255,255,0.2)',
                      border: '1px solid rgba(255,255,255,0.3)',
                      color: 'white',
                      fontFamily: 'Segoe UI, sans-serif',
                      fontWeight: '600',
                      borderRadius: '6px',
                      transition: 'all 0.2s ease-in-out'
                    }}
                    onMouseEnter={(e) => e.target.style.background = 'rgba(255,255,255,0.3)'}
                    onMouseLeave={(e) => e.target.style.background = 'rgba(255,255,255,0.2)'}
                  >
                    <FaEye className="me-1" />
                    View All
                  </Button>
                </div>
              </Card.Header>
              <Card.Body style={{ padding: '1.5rem' }}>
                <Table responsive hover style={{ marginBottom: 0 }}>
                  <thead>
                    <tr style={{ borderBottom: '2px solid #f8f9fa' }}>
                      <th style={{
                        fontFamily: 'Segoe UI, sans-serif',
                        fontWeight: '600',
                        color: '#333',
                        border: 'none',
                        paddingBottom: '1rem'
                      }}>Name</th>
                      <th style={{
                        fontFamily: 'Segoe UI, sans-serif',
                        fontWeight: '600',
                        color: '#333',
                        border: 'none',
                        paddingBottom: '1rem'
                      }}>Email</th>
                      <th style={{
                        fontFamily: 'Segoe UI, sans-serif',
                        fontWeight: '600',
                        color: '#333',
                        border: 'none',
                        paddingBottom: '1rem'
                      }}>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.slice(0, 3).map(user => (
                      <tr key={user._id} style={{ borderBottom: '1px solid #f8f9fa' }}>
                        <td style={{
                          fontFamily: 'Segoe UI, sans-serif',
                          color: '#333',
                          border: 'none',
                          padding: '1rem 0.75rem'
                        }}>{user.name}</td>
                        <td style={{
                          fontFamily: 'Segoe UI, sans-serif',
                          color: '#666',
                          border: 'none',
                          padding: '1rem 0.75rem'
                        }}>{user.email}</td>
                        <td style={{ border: 'none', padding: '1rem 0.75rem' }}>
                          <Badge style={{
                            background: user.status === 'Active' ?
                              'linear-gradient(135deg, #28a745 0%, #20c997 100%)' :
                              'linear-gradient(135deg, #6c757d 0%, #5a6268 100%)',
                            border: 'none',
                            borderRadius: '12px',
                            padding: '4px 10px',
                            fontFamily: 'Segoe UI, sans-serif',
                            fontSize: '0.75rem'
                          }}>
                            {user.status || 'Active'}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          </Col>

          {/* Recent Orders */}
          <Col lg={6} className="mb-4">
            <Card className="shadow-lg" style={{ background: 'rgba(255,255,255,0.95)' }}>
              <Card.Header style={{ background: '#8b4513', color: 'white' }}>
                <div className="d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">Recent Orders</h5>
                  <Button variant="light" size="sm" onClick={() => navigate('/orders')}>
                    <FaEye className="me-1" />
                    View All
                  </Button>
                </div>
              </Card.Header>
              <Card.Body>
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Customer</th>
                      <th>Book</th>
                      <th>Amount</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.slice(0, 3).map(order => (
                      <tr key={order._id}>
                        <td>{order.customerName}</td>
                        <td>{order.bookTitle}</td>
                        <td>₹{order.amount}</td>
                        <td>
                          <Badge bg={
                            order.status === 'Delivered' ? 'success' :
                            order.status === 'Processing' ? 'info' : 'warning'
                          }>
                            {order.status}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
      <Footer />
    </div>
  );
}

export default Ahome;
