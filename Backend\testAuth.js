const axios = require('axios');

const BASE_URL = 'http://localhost:4000';

async function testAuthentication() {
  console.log('🔐 Testing Authentication Flow...\n');
  
  const testUser = {
    name: 'Test User Auth',
    email: '<EMAIL>',
    password: 'password123'
  };

  try {
    // Test 1: Signup
    console.log('1️⃣ Testing Signup...');
    const signupResponse = await axios.post(`${BASE_URL}/signup`, testUser);
    console.log('✅ Signup Response:', signupResponse.data);
    
    if (signupResponse.data.success) {
      console.log('✅ Signup successful!\n');
    } else {
      console.log('❌ Signup failed:', signupResponse.data.message, '\n');
      return;
    }

    // Test 2: Login
    console.log('2️⃣ Testing Login...');
    const loginResponse = await axios.post(`${BASE_URL}/api/login`, {
      email: testUser.email,
      password: testUser.password
    });
    console.log('✅ Login Response:', loginResponse.data);
    
    if (loginResponse.data.success) {
      console.log('✅ Login successful!');
      console.log('👤 User Info:', loginResponse.data.user);
    } else {
      console.log('❌ Login failed:', loginResponse.data.message);
    }

    // Test 3: Test wrong password
    console.log('\n3️⃣ Testing Wrong Password...');
    try {
      const wrongPasswordResponse = await axios.post(`${BASE_URL}/api/login`, {
        email: testUser.email,
        password: 'wrongpassword'
      });
      console.log('🔍 Wrong Password Response:', wrongPasswordResponse.data);
      
      if (!wrongPasswordResponse.data.success) {
        console.log('✅ Correctly rejected wrong password');
      } else {
        console.log('❌ Should have rejected wrong password');
      }
    } catch (err) {
      console.log('❌ Error testing wrong password:', err.message);
    }

    // Test 4: Test non-existent user
    console.log('\n4️⃣ Testing Non-existent User...');
    try {
      const nonExistentResponse = await axios.post(`${BASE_URL}/api/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('🔍 Non-existent User Response:', nonExistentResponse.data);
      
      if (!nonExistentResponse.data.success) {
        console.log('✅ Correctly rejected non-existent user');
      } else {
        console.log('❌ Should have rejected non-existent user');
      }
    } catch (err) {
      console.log('❌ Error testing non-existent user:', err.message);
    }

    console.log('\n🎉 Authentication flow test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testAuthentication();
