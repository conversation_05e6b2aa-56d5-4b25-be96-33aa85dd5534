import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate, useParams } from 'react-router-dom';

import Unavbar from './Unavbar';

function OrderBook() {
  const [book, setBook] = useState(null);
  const [formData, setFormData] = useState({
    address: '',
    city: '',
    pincode: '',
    state: '',
  });

  const { id } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    // Fetch book details by id
    axios.get(`http://localhost:8000/books/${id}`)
      .then((resp) => {
        setBook(resp.data);
      })
      .catch(() => {
        console.log("Could not fetch book data");
      });
  }, [id]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const user = JSON.parse(localStorage.getItem('user'));
      const orderData = {
        ...formData,
        bookId: book._id,
        bookTitle: book.title,
        bookAuthor: book.author,
        bookPrice: book.price,
        bookCover: book.coverImage,
        userId: user.id,
        userName: user.name,
        totalAmount: book.price,
      };
      await axios.post('http://localhost:8000/orders', orderData);
      alert('Book ordered successfully!');
      navigate('/user/orders');
    } catch (error) {
      console.error('Error placing book order:', error);
      alert('Failed to place order. Please try again.');
    }
  };

  return (
    <div style={{ backgroundColor: "#f5f5f5", minHeight: "100vh" }}>
      <Unavbar />
      <div style={{ display: 'flex', justifyContent: 'center', marginTop: '40px' }}>
        <div className="max-w-md mx-auto p-6 border rounded shadow-lg bg-white">
          <h2 className="text-2xl font-semibold mb-2">Complete Your Book Order</h2>
          <p className="mb-4">Please provide your shipping details to order your book.</p>
          {book && (
            <div style={{ marginBottom: "24px" }}>
              <div style={{ display: "flex", justifyContent: "center", marginBottom: "12px" }}>
                <img
                  src={process.env.PUBLIC_URL + `/covers/${book.coverImage}`}
                  alt={book.title}
                  style={{ width: "120px", height: "170px", objectFit: "cover", borderRadius: "6px", boxShadow: "0 2px 8px #ccc" }}
                />
              </div>
              <h3 className="text-lg font-bold text-center">{book.title}</h3>
              <p className="text-center text-gray-600 mb-1">by {book.author}</p>
              <div style={{ display: "flex", justifyContent: "space-between", marginTop: "8px" }}>
                <span className="font-semibold">Price:</span>
                <span>₹{book.price}</span>
              </div>
            </div>
          )}
          <form onSubmit={handleSubmit}>
            <label className="block text-gray-600 mb-1">Address:</label>
            <input
              type="text"
              name="address"
              className="w-full p-2 border border-gray-300 rounded mb-3 focus:outline-none"
              placeholder="Flat/House No, Street"
              value={formData.address}
              onChange={handleChange}
              required
            />
            <div style={{ display: "flex", gap: "10px", marginBottom: "12px" }}>
              <input
                type="text"
                name="city"
                className="w-1/2 p-2 border border-gray-300 rounded focus:outline-none"
                placeholder="City"
                value={formData.city}
                onChange={handleChange}
                required
              />
              <input
                type="text"
                name="pincode"
                className="w-1/2 p-2 border border-gray-300 rounded focus:outline-none"
                placeholder="Pincode"
                value={formData.pincode}
                onChange={handleChange}
                required
              />
            </div>
            <input
              type="text"
              name="state"
              className="w-full p-2 border border-gray-300 rounded mb-4 focus:outline-none"
              placeholder="State"
              value={formData.state}
              onChange={handleChange}
              required
            />
            <button
              type="submit"
              className="w-full bg-blue-500 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Place Order
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}

export default OrderBook;
