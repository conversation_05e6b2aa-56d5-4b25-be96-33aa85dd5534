/* Status and Error Message Styles */
.status-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
}

.status-message {
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideIn 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.status-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.status-message.success {
  background: linear-gradient(135deg, rgba(72, 187, 120, 0.9), rgba(56, 161, 105, 0.9));
  color: white;
}

.status-message.success::before {
  background: linear-gradient(90deg, #48bb78, #38a169);
}

.status-message.error {
  background: linear-gradient(135deg, rgba(245, 101, 101, 0.9), rgba(229, 62, 62, 0.9));
  color: white;
}

.status-message.error::before {
  background: linear-gradient(90deg, #f56565, #e53e3e);
}

.status-message.warning {
  background: linear-gradient(135deg, rgba(237, 137, 54, 0.9), rgba(221, 107, 32, 0.9));
  color: white;
}

.status-message.warning::before {
  background: linear-gradient(90deg, #ed8936, #dd6b20);
}

.status-message.info {
  background: linear-gradient(135deg, rgba(66, 153, 225, 0.9), rgba(49, 130, 206, 0.9));
  color: white;
}

.status-message.info::before {
  background: linear-gradient(90deg, #4299e1, #3182ce);
}

.status-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.status-icon {
  font-size: 20px;
  font-weight: bold;
}

.status-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.status-description {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
  line-height: 1.4;
}

.status-close {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  color: inherit;
  font-size: 18px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  padding: 4px;
  border-radius: 4px;
}

.status-close:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

/* Connection Status Indicator */
.connection-status {
  position: fixed;
  bottom: 20px;
  left: 20px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.connection-status.online {
  background: linear-gradient(135deg, rgba(72, 187, 120, 0.9), rgba(56, 161, 105, 0.9));
  color: white;
}

.connection-status.offline {
  background: linear-gradient(135deg, rgba(245, 101, 101, 0.9), rgba(229, 62, 62, 0.9));
  color: white;
}

.connection-status.testing {
  background: linear-gradient(135deg, rgba(237, 137, 54, 0.9), rgba(221, 107, 32, 0.9));
  color: white;
}

.connection-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
}

.loading-content {
  background: white;
  padding: 40px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  max-width: 300px;
}

.loading-spinner-large {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.loading-description {
  font-size: 14px;
  color: #718096;
}

/* Error Page Styles */
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.error-content {
  background: white;
  padding: 60px 40px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.error-icon {
  font-size: 64px;
  color: #f56565;
  margin-bottom: 24px;
}

.error-title {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 16px;
}

.error-description {
  font-size: 16px;
  color: #718096;
  margin-bottom: 32px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.error-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.error-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  color: white;
  text-decoration: none;
}

.error-btn.secondary {
  background: #f8fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.error-btn.secondary:hover {
  background: #edf2f7;
  transform: translateY(-2px);
  text-decoration: none;
}

/* Animations */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .status-container {
    left: 20px;
    right: 20px;
    max-width: none;
  }
  
  .connection-status {
    left: 50%;
    transform: translateX(-50%);
  }
  
  .error-content {
    padding: 40px 24px;
  }
  
  .error-title {
    font-size: 24px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-btn {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }
}
