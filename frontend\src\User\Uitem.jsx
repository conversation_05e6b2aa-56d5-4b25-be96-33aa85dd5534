import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import Unavbar from './Unavbar';

/**
 * Uitem - Book Details Page
 * Fetches and displays details for a single book.
 * Assumes book cover images are available in the public folder.
 */
const Uitem = () => {
  const { id } = useParams();
  const [book, setBook] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch book details from backend
    const fetchBook = async () => {
      try {
        const resp = await fetch(`https://booknest-backend-55yh.onrender.com/books/${id}`);
        if (!resp.ok) throw new Error('Book not found');
        const data = await resp.json();
        setBook(data);
      } catch (err) {
        setBook(null);
      } finally {
        setLoading(false);
      }
    };
    fetchBook();
  }, [id]);

  return (
    <div>
      <Unavbar />
      <div style={{ minHeight: "100vh", background: "#f7f8fa" }}>
        {loading ? (
          <div style={{ textAlign: "center", marginTop: "120px", color: "#888" }}>
            <h2>Loading book details...</h2>
          </div>
        ) : book ? (
          <div className="container" style={{ maxWidth: "950px", margin: "40px auto", background: "#fff", borderRadius: "12px", boxShadow: "0 4px 24px rgba(0,0,0,0.08)", padding: "32px" }}>
            <div style={{ display: "flex", flexDirection: "row", gap: "48px", alignItems: "flex-start" }}>
              <div style={{ flex: "0 0 320px", display: "flex", justifyContent: "center", alignItems: "center", height: "480px" }}>
                <img
                  src={book.coverImage ? `/${book.coverImage}` : (book.itemImage ? `/${book.itemImage}` : "/default-book-cover.jpg")}
                  alt={book.title}
                  style={{
                    maxHeight: "100%",
                    maxWidth: "100%",
                    boxShadow: "0 4px 16px rgba(0,0,0,0.15)",
                    borderRadius: "10px",
                    background: "#f0f0f0"
                  }}
                  onError={e => { e.target.src = "/default-book-cover.jpg"; }}
                />
              </div>
              <div style={{ flex: "1" }}>
                <h1 style={{ fontWeight: "bold", fontSize: "2.3rem", marginBottom: "12px", color: "#1a237e" }}>{book.title}</h1>
                <p style={{ fontSize: "1.15rem", color: "#444", marginBottom: "6px" }}>
                  <strong>Author:</strong> {book.author}
                </p>
                <p style={{ fontSize: "1.1rem", color: "#555", marginBottom: "6px" }}>
                  <strong>Genre:</strong> {book.genre}
                </p>
                <p style={{ fontSize: "1.1rem", color: "#2d8659", fontWeight: "bold", marginBottom: "6px" }}>
                  <strong>Price:</strong> ₹{book.price}
                </p>
                <p style={{ fontSize: "1.05rem", color: "#555", marginBottom: "6px" }}>
                  <strong>Seller:</strong> {book.sellerName || book.userName || "Book Store"}
                </p>
                <hr style={{ margin: "22px 0" }} />
                <h3 style={{ color: "#333", marginBottom: "10px" }}>Description</h3>
                <p style={{ fontSize: "1.08rem", color: "#444", marginBottom: "30px" }}>
                  {book.description || "No description available for this book."}
                </p>
                <div>
                  <Link to={`/orderbook/${book._id}`}>
                    <button
                      type="button"
                      style={{
                        background: "#2563eb",
                        color: "white",
                        fontWeight: "600",
                        padding: "14px 38px",
                        border: "none",
                        borderRadius: "7px",
                        fontSize: "1.15rem",
                        cursor: "pointer",
                        boxShadow: "0 2px 8px rgba(37,99,235,0.13)",
                        transition: "background 0.2s"
                      }}
                    >
                      Buy Now
                    </button>
                  </Link>
                  <Link to="/wishlist" style={{ marginLeft: "18px" }}>
                    <button
                      type="button"
                      style={{
                        background: "#fff",
                        color: "#2563eb",
                        fontWeight: "600",
                        padding: "14px 30px",
                        border: "2px solid #2563eb",
                        borderRadius: "7px",
                        fontSize: "1.08rem",
                        cursor: "pointer",
                        marginLeft: "8px",
                        boxShadow: "0 2px 8px rgba(37,99,235,0.07)",
                        transition: "background 0.2s"
                      }}
                    >
                      Add to Wishlist
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div style={{ textAlign: "center", marginTop: "120px", color: "#e53935" }}>
            <h2>Book not found.</h2>
            <Link to="/uproducts" style={{ color: "#2563eb", textDecoration: "underline" }}>Browse Books</Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default Uitem;
