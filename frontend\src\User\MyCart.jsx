import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, <PERSON>, <PERSON>, <PERSON><PERSON>, Badge, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { FaShoppingCart, FaPlus, FaMinus, FaTrash, FaArrowLeft, FaCreditCard } from 'react-icons/fa';
import Unavbar from './Unavbar';
import Footer from '../Components/Footer';

const MyCart = () => {
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    loadCartItems();
  }, []);

  const loadCartItems = () => {
    try {
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      setCartItems(cart);
    } catch (error) {
      console.error('Error loading cart:', error);
      setCartItems([]);
    } finally {
      setLoading(false);
    }
  };

  const updateQuantity = (itemId, newQuantity) => {
    if (newQuantity < 1) {
      removeFromCart(itemId);
      return;
    }

    const updatedCart = cartItems.map(item =>
      (item.id === itemId || item._id === itemId) ? { ...item, quantity: newQuantity } : item
    );

    setCartItems(updatedCart);
    localStorage.setItem('cart', JSON.stringify(updatedCart));
  };

  const removeFromCart = (itemId) => {
    const updatedCart = cartItems.filter(item => item.id !== itemId && item._id !== itemId);
    setCartItems(updatedCart);
    localStorage.setItem('cart', JSON.stringify(updatedCart));
  };

  const clearCart = () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      setCartItems([]);
      localStorage.removeItem('cart');
    }
  };

  const calculateTotal = () => {
    return cartItems.reduce((total, item) => {
      const price = typeof item.price === 'string' ? parseFloat(item.price.replace('₹', '')) : item.price;
      return total + (price * item.quantity);
    }, 0);
  };

  const getItemId = (item) => item.id || item._id;

  const getImageUrl = (imageName) => {
    if (imageName && imageName.includes('.jpeg')) {
      return `/${imageName}`;
    }
    return `/${imageName}.jpeg`;
  };

  const proceedToCheckout = () => {
    if (cartItems.length === 0) {
      alert('Your cart is empty!');
      return;
    }

    // Store order data for checkout
    const orderData = {
      items: cartItems,
      total: calculateTotal(),
      orderDate: new Date().toISOString(),
      deliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
    };

    localStorage.setItem('currentOrder', JSON.stringify(orderData));
    navigate('/checkout');
  };

  if (loading) {
    return (
      <div>
        <Unavbar />
        <Container className="mt-5">
          <div className="text-center">
            <Spinner animation="border" variant="primary" />
            <p className="mt-3">Loading your cart...</p>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)' }}>
      <Unavbar />

      <Container className="py-5">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center">
                <FaShoppingCart className="me-3" size={32} style={{ color: '#FF6600' }} />
                <h1 className="mb-0" style={{ fontFamily: 'Segoe UI, sans-serif', color: '#333', fontWeight: '600' }}>
                  My Shopping Cart
                </h1>
              </div>
              <Button
                onClick={() => navigate('/books')}
                className="d-flex align-items-center"
                style={{
                  backgroundColor: '#FF6600',
                  borderColor: '#FF6600',
                  color: 'white',
                  fontFamily: 'Segoe UI, sans-serif',
                  fontWeight: '600',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '8px 16px'
                }}
                onMouseOver={(e) => e.target.style.backgroundColor = '#e55a00'}
                onMouseOut={(e) => e.target.style.backgroundColor = '#FF6600'}
              >
                <FaArrowLeft className="me-2" />
                Continue Shopping
              </Button>
            </div>
          </Col>
        </Row>

        {cartItems.length === 0 ? (
          <Row className="justify-content-center">
            <Col md={6}>
              <Card className="text-center shadow-lg" style={{ background: 'rgba(255,255,255,0.95)' }}>
                <Card.Body className="py-5">
                  <FaShoppingCart size={64} className="text-muted mb-3" />
                  <h4 className="text-dark mb-3">Your cart is empty</h4>
                  <p className="text-muted mb-4">Discover amazing books and add them to your cart!</p>
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => navigate('/books')}
                    style={{ background: '#8b4513', borderColor: '#8b4513' }}
                  >
                    Browse Books
                  </Button>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        ) : (
          <Row>
            <Col lg={8}>
              {cartItems.map((item, idx) => (
                <Card key={idx} className="mb-3 shadow-sm" style={{ background: 'rgba(255,255,255,0.95)' }}>
                  <Card.Body>
                    <Row className="align-items-center">
                      <Col md={3}>
                        <img
                          src={getImageUrl(item.image || item.title)}
                          alt={item.title}
                          className="img-fluid rounded"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = '/default_cover.svg';
                          }}
                          style={{ height: '120px', width: '90px', objectFit: 'cover' }}
                        />
                      </Col>
                      <Col md={5}>
                        <h5 className="mb-1" style={{ fontFamily: 'Playfair Display, serif', color: '#2c1810' }}>
                          {item.title}
                        </h5>
                        <p className="text-muted mb-1">by {item.author}</p>
                        <p className="text-muted mb-0">
                          <Badge bg="secondary">{item.category}</Badge>
                        </p>
                      </Col>
                      <Col md={2}>
                        <div className="d-flex align-items-center justify-content-center">
                          <Button
                            variant="outline-secondary"
                            size="sm"
                            onClick={() => updateQuantity(getItemId(item), item.quantity - 1)}
                            disabled={item.quantity <= 1}
                          >
                            <FaMinus />
                          </Button>
                          <span className="mx-3 fw-bold">{item.quantity}</span>
                          <Button
                            variant="outline-secondary"
                            size="sm"
                            onClick={() => updateQuantity(getItemId(item), item.quantity + 1)}
                          >
                            <FaPlus />
                          </Button>
                        </div>
                      </Col>
                      <Col md={2} className="text-end">
                        <div className="mb-2">
                          <strong style={{ color: '#8b4513' }}>
                            ₹{(typeof item.price === 'string' ? parseFloat(item.price.replace('₹', '')) : item.price) * item.quantity}
                          </strong>
                        </div>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => removeFromCart(getItemId(item))}
                        >
                          <FaTrash />
                        </Button>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>
              ))}

              <div className="mt-3">
                <Button
                  variant="outline-warning"
                  onClick={clearCart}
                >
                  <FaTrash className="me-2" />
                  Clear Cart
                </Button>
              </div>
            </Col>

            <Col lg={4}>
              <Card className="shadow-lg sticky-top" style={{ background: 'rgba(255,255,255,0.95)', top: '20px' }}>
                <Card.Header style={{ background: '#8b4513', color: 'white' }}>
                  <h5 className="mb-0">Order Summary</h5>
                </Card.Header>
                <Card.Body>
                  <div className="d-flex justify-content-between mb-2">
                    <span>Total Items:</span>
                    <Badge bg="primary">{cartItems.reduce((sum, item) => sum + item.quantity, 0)}</Badge>
                  </div>
                  <div className="d-flex justify-content-between mb-2">
                    <span>Subtotal:</span>
                    <span>₹{calculateTotal().toFixed(2)}</span>
                  </div>
                  <div className="d-flex justify-content-between mb-2">
                    <span>Shipping:</span>
                    <span className="text-success">Free</span>
                  </div>
                  <hr />
                  <div className="d-flex justify-content-between mb-3">
                    <strong>Total:</strong>
                    <strong style={{ color: '#8b4513', fontSize: '1.2rem' }}>₹{calculateTotal().toFixed(2)}</strong>
                  </div>
                  <Button
                    variant="primary"
                    size="lg"
                    className="w-100"
                    onClick={proceedToCheckout}
                    style={{ background: '#8b4513', borderColor: '#8b4513' }}
                  >
                    <FaCreditCard className="me-2" />
                    Proceed to Checkout
                  </Button>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        )}
      </Container>
      <Footer />
    </div>
  );
};

export default MyCart;
