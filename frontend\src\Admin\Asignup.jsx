import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useNavigate } from 'react-router-dom';

// Dummy data for books
const books = [
  {
    id: 1,
    title: "The Great Gatsby",
    author: "<PERSON><PERSON>",
    price: 12.99,
    cover: "/covers/gatsby.jpg",
    description: "A classic novel of the Roaring Twenties."
  },
  {
    id: 2,
    title: "To Kill a Mockingbird",
    author: "Harper Lee",
    price: 10.99,
    cover: "/covers/mockingbird.jpg",
    description: "A story of racial injustice in the Deep South."
  },
  {
    id: 3,
    title: "1984",
    author: "<PERSON>",
    price: 14.99,
    cover: "/covers/1984.jpg",
    description: "A dystopian novel about totalitarianism."
  },
  {
    id: 4,
    title: "Pride and Prejudice",
    author: "<PERSON>",
    price: 9.99,
    cover: "/covers/pride.jpg",
    description: "A romantic novel of manners."
  }
];

// Home Page
function Home() {
  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-indigo-600 text-white p-6 flex justify-between items-center">
        <h1 className="text-3xl font-bold">Book Store</h1>
        <nav>
          <Link to="/" className="mr-4 hover:underline">Home</Link>
          <Link to="/books" className="mr-4 hover:underline">Books</Link>
          <Link to="/cart" className="mr-4 hover:underline">Cart</Link>
          <Link to="/login" className="hover:underline">Login</Link>
        </nav>
      </header>
      <main className="flex flex-col items-center justify-center py-20">
        <h2 className="text-4xl font-bold mb-4">Welcome to the Book Store!</h2>
        <p className="text-lg mb-8">Discover your next great read from our collection.</p>
        <Link to="/books" className="bg-indigo-500 text-white px-6 py-3 rounded hover:bg-indigo-700 transition">Browse Books</Link>
      </main>
    </div>
  );
}

// Books Listing Page
function Books() {
  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-indigo-600 text-white p-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold">Books</h1>
        <nav>
          <Link to="/" className="mr-4 hover:underline">Home</Link>
          <Link to="/cart" className="hover:underline">Cart</Link>
        </nav>
      </header>
      <main className="p-8 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
        {books.map(book => (
          <div key={book.id} className="bg-white rounded shadow p-4 flex flex-col">
            <img src={book.cover} alt={book.title} className="h-48 w-full object-cover rounded mb-4" />
            <h3 className="text-xl font-semibold">{book.title}</h3>
            <p className="text-gray-600">{book.author}</p>
            <p className="text-indigo-600 font-bold mt-2">${book.price.toFixed(2)}</p>
            <Link to={`/books/${book.id}`} className="mt-4 bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-700 text-center">View Details</Link>
          </div>
        ))}
      </main>
    </div>
  );
}

// Book Details Page
function BookDetails({ bookId, addToCart }) {
  const book = books.find(b => b.id === parseInt(bookId));
  const navigate = useNavigate();

  if (!book) {
    return <div className="p-8">Book not found.</div>;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-indigo-600 text-white p-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold">{book.title}</h1>
        <nav>
          <Link to="/books" className="hover:underline">Back to Books</Link>
        </nav>
      </header>
      <main className="p-8 flex flex-col md:flex-row items-center">
        <img src={book.cover} alt={book.title} className="h-64 w-48 object-cover rounded mb-8 md:mb-0 md:mr-8" />
        <div>
          <h2 className="text-3xl font-bold mb-2">{book.title}</h2>
          <p className="text-lg text-gray-700 mb-2">by {book.author}</p>
          <p className="mb-4">{book.description}</p>
          <p className="text-2xl text-indigo-600 font-bold mb-4">${book.price.toFixed(2)}</p>
          <button
            onClick={() => { addToCart(book); navigate('/cart'); }}
            className="bg-indigo-500 text-white px-6 py-2 rounded hover:bg-indigo-700"
          >
            Add to Cart
          </button>
        </div>
      </main>
    </div>
  );
}

// Cart Page
function Cart({ cart, removeFromCart }) {
  const total = cart.reduce((sum, item) => sum + item.price, 0);

  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-indigo-600 text-white p-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold">Your Cart</h1>
        <nav>
          <Link to="/books" className="hover:underline">Back to Books</Link>
        </nav>
      </header>
      <main className="p-8">
        {cart.length === 0 ? (
          <p className="text-lg">Your cart is empty.</p>
        ) : (
          <div>
            <ul>
              {cart.map((item, idx) => (
                <li key={idx} className="flex items-center mb-4 bg-white p-4 rounded shadow">
                  <img src={item.cover} alt={item.title} className="h-16 w-12 object-cover rounded mr-4" />
                  <div className="flex-1">
                    <h3 className="font-semibold">{item.title}</h3>
                    <p className="text-gray-600">{item.author}</p>
                  </div>
                  <span className="font-bold text-indigo-600 mr-4">${item.price.toFixed(2)}</span>
                  <button
                    onClick={() => removeFromCart(idx)}
                    className="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-700"
                  >
                    Remove
                  </button>
                </li>
              ))}
            </ul>
            <div className="mt-6 text-right">
              <span className="text-xl font-bold">Total: ${total.toFixed(2)}</span>
              <button className="ml-6 bg-green-500 text-white px-6 py-2 rounded hover:bg-green-700">Checkout</button>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

// Simple Login Page (no backend, just for demo)
function Login() {
  const navigate = useNavigate();
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');

  function handleLogin(e) {
    e.preventDefault();
    // For demo, just redirect
    alert('Logged in!');
    navigate('/');
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="max-w-md w-full bg-white p-8 rounded-md shadow-md">
        <h2 className="text-3xl font-extrabold text-gray-900 mb-6 text-center">Login</h2>
        <form className="space-y-6" onSubmit={handleLogin}>
          <div>
            <label className="block text-sm font-medium text-gray-700">Email address</label>
            <input
              name="email"
              type="email"
              autoComplete="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md"
              placeholder="Email address"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Password</label>
            <input
              name="password"
              type="password"
              autoComplete="current-password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md"
              placeholder="Password"
              required
            />
          </div>
          <button
            type="submit"
            className="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded w-full"
          >
            Login
          </button>
        </form>
      </div>
    </div>
  );
}

// Main App with Routing and Cart State
function BookStoreApp() {
  const [cart, setCart] = React.useState([]);

  function addToCart(book) {
    setCart([...cart, book]);
  }

  function removeFromCart(idx) {
    setCart(cart.filter((_, i) => i !== idx));
  }

  return (
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/books" element={<Books />} />
        <Route
          path="/books/:id"
          element={
            <BookDetailsWrapper addToCart={addToCart} />
          }
        />
        <Route path="/cart" element={<Cart cart={cart} removeFromCart={removeFromCart} />} />
        <Route path="/login" element={<Login />} />
      </Routes>
    </Router>
  );
}

// Wrapper to get book id from URL params
function BookDetailsWrapper({ addToCart }) {
  const { id } = require('react-router-dom').useParams();
  return <BookDetails bookId={id} addToCart={addToCart} />;
}

export default BookStoreApp;
