import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaB<PERSON>, FaShoppingCart, FaUser, FaHistory, FaClock, FaGift, FaArrowRight, FaStar, FaHeart, FaSearch, FaTrophy, FaBookOpen, FaChartLine } from 'react-icons/fa';
import axios from 'axios';
import './BookStore.css';
import './WelcomeAnimations.css';
import FloatingActions from '../Components/FloatingActions';

const Welcome = () => {
  const [user, setUser] = useState(null);
  const [recentOrders, setRecentOrders] = useState([]);
  const [cartCount, setCartCount] = useState(0);
  const [loading, setLoading] = useState(true);

  // Interactive states
  const [currentTime, setCurrentTime] = useState(new Date());
  const [hoveredCard, setHoveredCard] = useState(null);
  const [animationStep, setAnimationStep] = useState(0);
  const [userStats, setUserStats] = useState({
    totalOrders: 0,
    totalSpent: 0,
    favoriteGenre: 'Fiction'
  });

  const navigate = useNavigate();

  useEffect(() => {
    const initializeWelcome = async () => {
      // Get user from localStorage
      const userData = localStorage.getItem('user');
      if (userData) {
        try {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);

          // Fetch user's recent orders
          try {
            const ordersResponse = await axios.get(`https://booknest-backend-55yh.onrender.com/getorders/${parsedUser.id}`);
            const orders = ordersResponse.data;
            setRecentOrders(orders.slice(0, 3)); // Get last 3 orders

            // Calculate user stats
            const totalSpent = orders.reduce((sum, order) => sum + (order.totalamount || 0), 0);
            setUserStats({
              totalOrders: orders.length,
              totalSpent: totalSpent,
              favoriteGenre: 'Fiction' // Could be calculated from order data
            });
          } catch (error) {
            console.error('Error fetching orders:', error);
            setRecentOrders([]);
          }

          // Get cart count from localStorage
          const cart = localStorage.getItem('cart');
          if (cart) {
            try {
              const cartItems = JSON.parse(cart);
              setCartCount(cartItems.length);
            } catch (e) {
              setCartCount(0);
            }
          }

        } catch (e) {
          console.error('Error parsing user data:', e);
          // Don't redirect to login, just set user to null
          setUser(null);
        }
      } else {
        // If no user data, redirect to main welcome page
        navigate('/');
        return;
      }
      setLoading(false);
    };

    initializeWelcome();

    // Animation timer
    const animationTimer = setInterval(() => {
      setAnimationStep(prev => (prev + 1) % 4);
    }, 2000);

    // Clock timer
    const clockTimer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => {
      clearInterval(animationTimer);
      clearInterval(clockTimer);
    };
  }, [navigate]);



  const handleBrowseBooks = () => {
    navigate('/books');
  };

  // Interactive helper functions
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const getMotivationalQuote = () => {
    const quotes = [
      "A reader lives a thousand lives before he dies.",
      "Books are a uniquely portable magic.",
      "Reading is to the mind what exercise is to the body.",
      "The more that you read, the more things you will know."
    ];
    return quotes[animationStep];
  };

  const getProgressColor = (index) => {
    const colors = ['bg-teal-500', 'bg-blue-500', 'bg-green-500', 'bg-purple-500'];
    return colors[index % colors.length];
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-teal-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading BookNest...</p>
        </div>
      </div>
    );
  }

  // If no user, the useEffect will redirect to login
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-teal-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <FaBook className="text-6xl text-teal-600 mx-auto mb-4 animate-spin" />
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Redirecting to Login...</h2>
          <p className="text-gray-600">Please wait while we redirect you to the login page.</p>
        </div>
      </div>
    );
  }



  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 via-blue-50 to-indigo-50">
      {/* Enhanced Interactive Header */}
      <header className="bg-gradient-to-r from-teal-600 to-blue-600 shadow-2xl">
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <FaBook className="text-4xl text-white animate-pulse" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-ping"></div>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">BookNest</h1>
                <p className="text-teal-100 text-sm">{getGreeting()}, {user.name}!</p>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="text-center">
                <div className="text-white text-sm opacity-75">Local Time</div>
                <div className="text-white font-mono text-lg">
                  {currentTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                </div>
              </div>
              <div className="flex items-center space-x-3 bg-white bg-opacity-20 rounded-full px-4 py-2">
                <FaUser className="text-white" />
                <span className="font-medium text-white">{user.name}</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-16">
        <div className="max-w-4xl mx-auto text-center">
          {/* Enhanced Welcome Message with Animation */}
          <div className="mb-16 text-center">
            <div className="relative">
              <h2 className="text-6xl font-bold text-gray-800 mb-6 animate-fade-in">
                Welcome back, <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal-600 to-blue-600">{user.name}!</span>
              </h2>
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="flex space-x-2">
                  {[...Array(5)].map((_, i) => (
                    <FaStar
                      key={i}
                      className={`text-yellow-400 text-lg ${i <= animationStep ? 'animate-bounce' : 'opacity-30'}`}
                      style={{animationDelay: `${i * 0.2}s`}}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-teal-50 to-blue-50 rounded-2xl p-8 mb-8 border border-teal-100">
              <p className="text-2xl text-gray-700 leading-relaxed mb-4 font-light">
                "{getMotivationalQuote()}"
              </p>
              <div className="flex justify-center items-center space-x-4">
                <FaBookOpen className="text-teal-600 text-xl" />
                <span className="text-gray-600">Your reading journey continues...</span>
                <FaHeart className="text-red-500 text-xl animate-pulse" />
              </div>
            </div>

            {/* Interactive Stats Bar */}
            <div className="bg-white rounded-2xl shadow-xl p-6 mb-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center group cursor-pointer transform hover:scale-105 transition-all duration-300">
                  <div className="bg-gradient-to-br from-teal-400 to-teal-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:shadow-lg">
                    <FaTrophy className="text-white text-2xl" />
                  </div>
                  <div className="text-3xl font-bold text-teal-600 mb-1">{userStats.totalOrders}</div>
                  <div className="text-gray-600">Total Orders</div>
                </div>

                <div className="text-center group cursor-pointer transform hover:scale-105 transition-all duration-300">
                  <div className="bg-gradient-to-br from-blue-400 to-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:shadow-lg">
                    <FaChartLine className="text-white text-2xl" />
                  </div>
                  <div className="text-3xl font-bold text-blue-600 mb-1">₹{userStats.totalSpent}</div>
                  <div className="text-gray-600">Total Spent</div>
                </div>

                <div className="text-center group cursor-pointer transform hover:scale-105 transition-all duration-300">
                  <div className="bg-gradient-to-br from-purple-400 to-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:shadow-lg">
                    <FaHeart className="text-white text-2xl" />
                  </div>
                  <div className="text-xl font-bold text-purple-600 mb-1">{userStats.favoriteGenre}</div>
                  <div className="text-gray-600">Favorite Genre</div>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Interactive Action Cards */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {/* Browse Books Card */}
            <div
              className="bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:rotate-1 cursor-pointer group"
              onMouseEnter={() => setHoveredCard('browse')}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <div className="text-center">
                <div className="bg-gradient-to-br from-teal-100 to-teal-200 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <FaSearch className={`text-4xl text-teal-600 ${hoveredCard === 'browse' ? 'animate-bounce' : ''}`} />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-teal-600 transition-colors">Browse Books</h3>
                <p className="text-gray-600 mb-6 group-hover:text-gray-700">
                  Explore our vast collection of books across all genres. Find your next great read!
                </p>
                <div className="space-y-3">
                  <button
                    onClick={handleBrowseBooks}
                    className="w-full bg-gradient-to-r from-teal-600 to-teal-700 text-white px-8 py-3 rounded-xl font-semibold hover:from-teal-700 hover:to-teal-800 transition-all transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    <FaBook className="inline mr-2" />
                    Start Browsing
                  </button>
                  <div className="text-sm text-gray-500">
                    1000+ books available
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions Card */}
            <div
              className="bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:-rotate-1 cursor-pointer group"
              onMouseEnter={() => setHoveredCard('actions')}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <div className="text-center">
                <div className="bg-gradient-to-br from-blue-100 to-blue-200 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <FaShoppingCart className={`text-4xl text-blue-600 ${hoveredCard === 'actions' ? 'animate-bounce' : ''}`} />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors">Quick Actions</h3>
                <p className="text-gray-600 mb-6 group-hover:text-gray-700">
                  Access your cart and orders with just one click.
                </p>
                <div className="space-y-3">
                  <button
                    onClick={() => navigate('/mycart')}
                    className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-blue-800 transition-all flex items-center justify-between group-hover:scale-105"
                  >
                    <span className="flex items-center">
                      <FaShoppingCart className="mr-2" />
                      My Cart
                    </span>
                    {cartCount > 0 && (
                      <span className="bg-blue-800 text-white px-3 py-1 rounded-full text-sm animate-pulse">
                        {cartCount}
                      </span>
                    )}
                  </button>

                  <button
                    onClick={() => navigate('/myorders-new')}
                    className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-xl font-medium hover:from-green-700 hover:to-green-800 transition-all flex items-center justify-between group-hover:scale-105"
                  >
                    <span className="flex items-center">
                      <FaHistory className="mr-2" />
                      My Orders
                    </span>
                    {recentOrders.length > 0 && (
                      <span className="bg-green-800 text-white px-3 py-1 rounded-full text-sm">
                        {recentOrders.length}
                      </span>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* New Interactive Features Card */}
            <div
              className="bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:rotate-1 cursor-pointer group"
              onMouseEnter={() => setHoveredCard('features')}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <div className="text-center">
                <div className="bg-gradient-to-br from-purple-100 to-purple-200 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <FaGift className={`text-4xl text-purple-600 ${hoveredCard === 'features' ? 'animate-bounce' : ''}`} />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-purple-600 transition-colors">Special Features</h3>
                <p className="text-gray-600 mb-6 group-hover:text-gray-700">
                  Discover personalized recommendations and exclusive offers.
                </p>
                <div className="space-y-3">
                  <button
                    onClick={() => navigate('/uhome')}
                    className="w-full bg-gradient-to-r from-purple-600 to-purple-700 text-white px-6 py-3 rounded-xl font-medium hover:from-purple-700 hover:to-purple-800 transition-all group-hover:scale-105"
                  >
                    <FaStar className="inline mr-2" />
                    Recommendations
                  </button>
                  <div className="text-sm text-gray-500">
                    Curated just for you
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Recent Orders Section */}
          {recentOrders.length > 0 && (
            <div className="bg-white rounded-2xl shadow-xl p-8 mb-16 animate-slide-in-left">
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-3xl font-bold text-gray-800 flex items-center glow-text">
                  <FaHistory className="mr-3 text-blue-600 animate-pulse" />
                  Recent Orders
                </h3>
                <button
                  onClick={() => navigate('/myorders-new')}
                  className="interactive-button bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-blue-800 transition-all flex items-center shadow-lg hover:shadow-xl"
                >
                  View All <FaArrowRight className="ml-2 transform group-hover:translate-x-1 transition-transform" />
                </button>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                {recentOrders.map((order, index) => (
                  <div
                    key={index}
                    className="book-card card-hover-effect bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-2xl p-6 cursor-pointer group"
                    style={{animationDelay: `${index * 0.2}s`}}
                  >
                    <div className="book-card-inner">
                      <div className="book-card-front">
                        <div className="flex items-start space-x-4">
                          <div className={`w-16 h-20 ${getProgressColor(index)} rounded-lg flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                            <FaBookOpen className="text-white text-2xl" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-bold text-gray-800 text-lg mb-2 group-hover:text-blue-600 transition-colors">
                              {order.booktitle || 'Book Order'}
                            </h4>
                            <p className="text-gray-600 text-sm mb-2 italic">
                              by {order.bookauthor || 'Unknown Author'}
                            </p>
                            <div className="flex items-center justify-between">
                              <p className="text-green-600 font-bold text-xl">
                                ₹{order.totalamount}
                              </p>
                              <div className="flex items-center space-x-1">
                                {[...Array(5)].map((_, i) => (
                                  <FaStar key={i} className="text-yellow-400 text-sm" />
                                ))}
                              </div>
                            </div>
                            <div className="flex items-center text-sm text-gray-500 mt-3 bg-gray-100 rounded-full px-3 py-1">
                              <FaClock className="mr-2" />
                              {order.BookingDate}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Progress indicator */}
                    <div className="mt-4">
                      <div className="flex justify-between text-xs text-gray-500 mb-1">
                        <span>Order Status</span>
                        <span>Delivered</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="progress-bar h-2 rounded-full" style={{width: '100%'}}></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Order summary */}
              <div className="mt-8 bg-gradient-to-r from-blue-50 to-teal-50 rounded-xl p-6 border border-blue-100">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                  <div className="stats-counter">
                    <div className="text-2xl font-bold text-blue-600">{recentOrders.length}</div>
                    <div className="text-gray-600">Recent Orders</div>
                  </div>
                  <div className="stats-counter">
                    <div className="text-2xl font-bold text-green-600">
                      ₹{recentOrders.reduce((sum, order) => sum + (order.totalamount || 0), 0)}
                    </div>
                    <div className="text-gray-600">Total Value</div>
                  </div>
                  <div className="stats-counter">
                    <div className="text-2xl font-bold text-purple-600">100%</div>
                    <div className="text-gray-600">Satisfaction</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Enhanced Interactive Journey Section */}
          <div className="bg-white rounded-2xl shadow-xl p-8 mb-16 animate-slide-in-right">
            <h3 className="text-3xl font-bold text-gray-800 mb-8 text-center glow-text">Your BookNest Journey</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-6 group cursor-pointer card-hover-effect">
                <div className="floating-element bg-gradient-to-br from-teal-100 to-teal-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-all duration-300">
                  <FaSearch className="text-3xl text-teal-600" />
                </div>
                <div className="text-2xl font-bold text-teal-600 mb-2 stats-counter">Discover</div>
                <div className="text-gray-600 group-hover:text-gray-800 transition-colors">Thousands of books across all genres</div>
                <div className="mt-4 text-sm text-teal-600 font-medium">1000+ Books Available</div>
              </div>

              <div className="text-center p-6 group cursor-pointer card-hover-effect">
                <div className="floating-element bg-gradient-to-br from-blue-100 to-blue-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-all duration-300">
                  <FaShoppingCart className="text-3xl text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-blue-600 mb-2 stats-counter">Shop</div>
                <div className="text-gray-600 group-hover:text-gray-800 transition-colors">Easy & secure checkout experience</div>
                <div className="mt-4 text-sm text-blue-600 font-medium">100% Secure Payment</div>
              </div>

              <div className="text-center p-6 group cursor-pointer card-hover-effect">
                <div className="floating-element bg-gradient-to-br from-green-100 to-green-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-all duration-300">
                  <FaBookOpen className="text-3xl text-green-600" />
                </div>
                <div className="text-2xl font-bold text-green-600 mb-2 stats-counter">Enjoy</div>
                <div className="text-gray-600 group-hover:text-gray-800 transition-colors">Your personalized reading adventure</div>
                <div className="mt-4 text-sm text-green-600 font-medium">Curated Recommendations</div>
              </div>
            </div>

            {/* Interactive progress bar */}
            <div className="mt-8 bg-gray-100 rounded-full p-2">
              <div className="progress-bar h-4 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">Your Reading Journey Continues...</span>
              </div>
            </div>
          </div>

          {/* Enhanced Call to Action with Floating Elements */}
          <div className="text-center relative mb-16">
            {/* Floating background elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              <div className="floating-element absolute top-10 left-10 w-16 h-16 bg-teal-200 rounded-full opacity-20"></div>
              <div className="floating-element absolute top-20 right-20 w-12 h-12 bg-blue-200 rounded-full opacity-30"></div>
              <div className="floating-element absolute bottom-10 left-1/4 w-20 h-20 bg-purple-200 rounded-full opacity-25"></div>
              <div className="floating-element absolute bottom-20 right-1/3 w-14 h-14 bg-green-200 rounded-full opacity-20"></div>
            </div>

            <div className="relative z-10">
              <h3 className="text-4xl font-bold text-gray-800 mb-4 glow-text">
                Ready to dive into the world of books?
              </h3>
              <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Join thousands of readers who have discovered their next favorite book with BookNest.
                Your literary adventure awaits!
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button
                  onClick={handleBrowseBooks}
                  className="interactive-button bg-gradient-to-r from-teal-600 to-blue-600 text-white px-12 py-4 rounded-xl font-bold text-lg hover:from-teal-700 hover:to-blue-700 transition-all transform hover:scale-105 shadow-xl hover:shadow-2xl animate-pulse-glow"
                >
                  <FaBook className="inline mr-3" />
                  Explore Books Now
                  <FaArrowRight className="inline ml-3" />
                </button>

                <button
                  onClick={() => navigate('/uhome')}
                  className="interactive-button bg-white border-2 border-teal-600 text-teal-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-teal-600 hover:text-white transition-all transform hover:scale-105 shadow-lg"
                >
                  <FaStar className="inline mr-2" />
                  View Recommendations
                </button>
              </div>

              <div className="mt-8 flex justify-center space-x-8 text-sm text-gray-500">
                <div className="flex items-center">
                  <FaTrophy className="mr-2 text-yellow-500" />
                  Award-winning collection
                </div>
                <div className="flex items-center">
                  <FaHeart className="mr-2 text-red-500" />
                  Loved by readers
                </div>
                <div className="flex items-center">
                  <FaChartLine className="mr-2 text-green-500" />
                  Growing community
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Enhanced Interactive Footer */}
      <footer className="bg-gradient-to-r from-gray-800 to-gray-900 text-white py-12 mt-16">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <div className="text-center md:text-left">
              <div className="flex items-center justify-center md:justify-start mb-4">
                <FaBook className="text-3xl text-teal-400 mr-3 animate-pulse" />
                <h3 className="text-2xl font-bold">BookNest</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Your gateway to endless literary adventures. Discover, shop, and enjoy the world's finest books.
              </p>
            </div>

            <div className="text-center">
              <h4 className="text-lg font-semibold mb-4 text-teal-400">Quick Links</h4>
              <div className="space-y-2">
                <button onClick={() => navigate('/books')} className="block text-gray-300 hover:text-white transition-colors mx-auto">
                  Browse Books
                </button>
                <button onClick={() => navigate('/mycart')} className="block text-gray-300 hover:text-white transition-colors mx-auto">
                  My Cart
                </button>
                <button onClick={() => navigate('/myorders-new')} className="block text-gray-300 hover:text-white transition-colors mx-auto">
                  My Orders
                </button>
              </div>
            </div>

            <div className="text-center md:text-right">
              <h4 className="text-lg font-semibold mb-4 text-teal-400">Connect With Us</h4>
              <div className="flex justify-center md:justify-end space-x-4">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-colors">
                  <span className="text-sm font-bold">f</span>
                </div>
                <div className="w-10 h-10 bg-blue-400 rounded-full flex items-center justify-center cursor-pointer hover:bg-blue-500 transition-colors">
                  <span className="text-sm font-bold">t</span>
                </div>
                <div className="w-10 h-10 bg-pink-600 rounded-full flex items-center justify-center cursor-pointer hover:bg-pink-700 transition-colors">
                  <span className="text-sm font-bold">i</span>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-700 pt-6 text-center">
            <p className="text-gray-300 flex items-center justify-center">
              © {new Date().getFullYear()} BookNest. Made with
              <FaHeart className="text-red-500 mx-2 animate-pulse" />
              for book lovers worldwide.
            </p>
            <p className="text-gray-400 text-sm mt-2">
              Welcome back, {user.name}! Happy reading! 📚
            </p>
          </div>
        </div>
      </footer>

      {/* Enhanced Floating Actions */}
      <FloatingActions />

      {/* Floating Reading Progress Indicator */}
      <div className="fixed bottom-4 left-4 bg-white rounded-full shadow-lg p-3 animate-float">
        <div className="text-center">
          <FaBookOpen className="text-teal-600 text-xl mb-1" />
          <div className="text-xs text-gray-600">Keep Reading!</div>
        </div>
      </div>
    </div>
  );
};

export default Welcome;

