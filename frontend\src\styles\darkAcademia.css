/* Amazon/Flipkart Style Design System */
@import url('https://fonts.googleapis.com/css2?family=Segoe+UI:wght@300;400;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap');

/* Amazon/Flipkart Theme for BookNest */
:root {
  /* Amazon/Flipkart Primary Colors */
  --primary-orange: #FF6600;
  --primary-orange-dark: #e55a00;
  --primary-orange-light: #FF9900;
  --secondary-blue: #0066CC;
  --secondary-blue-dark: #004499;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-accent: #fff5f0;
  --bg-light-gray: #f1f3f4;

  /* Text Colors - High Contrast for Readability */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-muted: #999999;
  --text-white: #ffffff;
  --text-orange: #FF6600;

  /* Border Colors */
  --border-light: #e0e0e0;
  --border-medium: #cccccc;
  --border-orange: #FF6600;

  /* Shadow */
  --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
  --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);

  /* Fonts - Amazon/Flipkart Style */
  --font-primary: 'Segoe UI', 'Roboto', 'Open Sans', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-secondary: 'Roboto', 'Segoe UI', sans-serif;

  /* Legacy Dark Academia Variables (for backward compatibility) */
  --da-bg: var(--bg-secondary);
  --da-bg-alt: var(--bg-primary);
  --da-text: var(--text-primary);
  --da-accent: var(--primary-orange);
  --da-accent-dark: var(--primary-orange-dark);
  --da-border: var(--border-light);
  --da-card: var(--bg-primary);
  --da-link: var(--primary-orange);
  --da-link-hover: var(--primary-orange-dark);
  --da-shadow: var(--shadow-light);
  --da-font: var(--font-primary);

  /* Legacy Colors */
  --dark-academia-primary: var(--text-primary);
  --dark-academia-secondary: var(--primary-orange);
  --dark-academia-accent: var(--primary-orange-light);
  --text-accent: var(--primary-orange);
  --gold: #FFD700;
  --burgundy: #800020;
  --forest-green: #355e3b;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-primary);
  min-height: 100vh;
  line-height: 1.6;
  font-size: 14px;
}

/* Amazon/Flipkart Style Links */
a, .btn-link {
  color: var(--primary-orange);
  text-decoration: none;
  font-weight: 500;
}
a:hover, .btn-link:hover {
  color: var(--da-link-hover);
  text-decoration: underline;
}

/* Amazon/Flipkart Style Navigation */
.navbar, .navbar-dark {
  background: var(--bg-primary) !important;
  border-bottom: 2px solid var(--primary-orange);
  box-shadow: var(--shadow-light);
}

.navbar-brand img {
  height: 40px;
  margin-right: 10px;
}

/* Amazon/Flipkart Style Cards */
.card {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-light);
  border-radius: 8px;
  transition: box-shadow 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-medium);
}

/* Amazon/Flipkart Style Buttons */
.btn-da, .btn-primary {
  background: var(--primary-orange);
  color: var(--text-white);
  border: none;
  font-weight: 600;
  font-family: var(--font-primary);
  padding: 10px 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-da:hover, .btn-primary:hover {
  background: var(--primary-orange-dark);
  color: var(--text-white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: var(--bg-primary);
  color: var(--primary-orange);
  border: 2px solid var(--primary-orange);
  font-weight: 600;
  font-family: var(--font-primary);
  padding: 10px 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: var(--primary-orange);
  color: var(--text-white);
}

/* Amazon/Flipkart Style Headings */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-primary);
  color: var(--text-primary);
  font-weight: 600;
  line-height: 1.3;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 { font-size: 1rem; }

/* Amazon/Flipkart Style Form Elements */
input, select, textarea, .form-control {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 2px solid var(--border-light);
  border-radius: 6px;
  padding: 12px 16px;
  font-family: var(--font-primary);
  font-size: 14px;
  transition: border-color 0.3s ease;
}

input:focus, select:focus, textarea:focus, .form-control:focus {
  border-color: var(--primary-orange);
  box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
  outline: none;
}

/* Form Labels */
label, .form-label {
  color: var(--text-primary);
  font-weight: 600;
  font-family: var(--font-primary);
  margin-bottom: 8px;
  font-size: 14px;
}

::-webkit-scrollbar {
  width: 8px;
  background: var(--da-bg-alt);
}
::-webkit-scrollbar-thumb {
  background: var(--da-accent-dark);
  border-radius: 4px;
}

/* Amazon/Flipkart Typography Classes */
.font-serif-primary, .font-primary {
  font-family: var(--font-primary);
}

.font-serif-secondary, .font-secondary {
  font-family: var(--font-secondary);
}

.font-sans {
  font-family: var(--font-primary);
}

/* Amazon/Flipkart Style Hero Banner */
.hero-banner {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-accent) 50%, var(--bg-secondary) 100%);
  min-height: 60vh;
  position: relative;
  overflow: hidden;
  border-bottom: 3px solid var(--primary-orange);
}

.hero-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="books" patternUnits="userSpaceOnUse" width="20" height="20"><rect width="20" height="20" fill="none"/><path d="M2 2h16v16H2z" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23books)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  color: #f5f5dc;
  font-family: 'Crimson Text', 'Georgia', serif;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
  position: relative;
  z-index: 1;
}

/* Featured Books Section */
.featured-books {
  background: linear-gradient(to bottom, #faf0e6, #fff8dc);
}

.book-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.book-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.book-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 0%, rgba(139, 69, 19, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.book-card:hover::before {
  opacity: 1;
}

.book-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.book-card:hover .book-image {
  transform: scale(1.05);
}

/* Rating Stars */
.rating-stars {
  display: flex;
  align-items: center;
  gap: 2px;
}

.star {
  color: #ffd700;
  font-size: 16px;
}

.star.empty {
  color: #e5e5e5;
}

/* Categories Navigation */
.categories-nav {
  background: linear-gradient(to right, #8b4513, #d2b48c);
  padding: 1rem 0;
}

.category-btn {
  background: rgba(245, 245, 220, 0.9);
  color: #8b4513;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-family: 'Source Sans Pro', sans-serif;
}

.category-btn:hover {
  background: rgba(245, 245, 220, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Floating Action Buttons */
.floating-actions {
  position: fixed;
  top: 100px;
  right: 24px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.fab {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.cart-fab {
  background: linear-gradient(135deg, #d97706, #ea580c);
}

.orders-fab {
  background: linear-gradient(135deg, #2563eb, #4f46e5);
}

.fab-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* Header Cart Section */
.header-cart-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.cart-preview, .orders-link {
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Source Sans Pro', sans-serif;
}

.cart-preview {
  background: rgba(245, 245, 220, 0.8);
  border: 1px solid #d2b48c;
}

.cart-preview:hover {
  background: rgba(245, 245, 220, 1);
  transform: translateY(-2px);
}

.orders-link {
  background: rgba(219, 234, 254, 0.8);
  border: 1px solid #93c5fd;
}

.orders-link:hover {
  background: rgba(219, 234, 254, 1);
  transform: translateY(-2px);
}

/* Compact Login Styles - Amazon/Flipkart Inspired */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f7f7f7 0%, #ffffff 50%, #f0f0f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  font-family: 'Amazon Ember', 'Helvetica Neue', Arial, sans-serif;
}

.login-card {
  width: 100%;
  max-width: 420px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #ddd;
  padding: 2.5rem;
}

.login-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.login-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #FF6600, #FF9900);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  box-shadow: 0 2px 8px rgba(255, 102, 0, 0.3);
}

.demo-credentials {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.demo-credential-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.demo-credential-code {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  font-weight: 500;
}

.login-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #ffffff;
  color: #333;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  transition: all 0.2s ease;
}

.login-input:focus {
  outline: none;
  border-color: #FF6600;
  box-shadow: 0 0 0 2px rgba(255, 102, 0, 0.2);
}

.login-input::placeholder {
  color: #999;
}

.login-button {
  width: 100%;
  background: linear-gradient(135deg, #FF6600, #FF9900);
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 4px;
  font-weight: 600;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.login-button:hover {
  background: linear-gradient(135deg, #e55a00, #ff8800);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 102, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-banner {
    min-height: 40vh;
    padding: 2rem 1rem;
  }
  
  .floating-actions {
    top: auto;
    bottom: 20px;
    right: 20px;
    flex-direction: row;
  }
  
  .fab {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .header-cart-section {
    flex-direction: column;
    gap: 8px;
  }
  
  .categories-nav {
    padding: 0.5rem;
  }
  
  .category-btn {
    padding: 0.5rem 1rem;
    font-size: 14px;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Enhanced Responsive Design */
@media (max-width: 480px) {
  .hero-banner h1 {
    font-size: 2rem;
  }

  .featured-books-grid {
    grid-template-columns: 1fr;
  }

  .categories-nav {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .categories-nav::-webkit-scrollbar {
    display: none;
  }

  .fab {
    width: 45px;
    height: 45px;
  }

  .login-card {
    margin: 0.5rem;
    padding: 1rem;
  }
}

/* Enhanced Hover Effects */
.book-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(44, 24, 16, 0.3);
}

.category-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.2);
}

.fab:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Micro-interactions */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.fab-badge {
  animation: pulse 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.hero-cta:hover {
  animation: bounce 1s;
}

/* Brighter Form Inputs and Labels */
.form-label {
  color: #333 !important;
  font-weight: 600 !important;
  font-size: 0.95rem !important;
}

.form-control, .form-select {
  background-color: #ffffff !important;
  border: 2px solid #ddd !important;
  color: #333 !important;
  font-size: 1rem !important;
}

.form-control:focus, .form-select:focus {
  background-color: #ffffff !important;
  border-color: #8b4513 !important;
  box-shadow: 0 0 0 0.2rem rgba(139, 69, 19, 0.25) !important;
  color: #333 !important;
}

.form-control::placeholder {
  color: #666 !important;
  opacity: 0.8 !important;
}

/* Focus States for Accessibility */
.login-input:focus,
.search-input:focus {
  outline: none;
  ring: 2px solid var(--accent-color);
  border-color: var(--accent-color);
}

button:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .floating-actions,
  .hero-banner,
  .categories-nav {
    display: none;
  }

  .book-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
