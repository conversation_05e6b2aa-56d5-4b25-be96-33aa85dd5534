/* User Home Page Styles */
.user-home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.user-navbar {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  padding: 12px 0;
}

.user-navbar .navbar-brand {
  font-weight: 700;
  font-size: 24px;
  color: white !important;
}

.user-navbar .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  margin: 0 8px;
  padding: 8px 16px !important;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.user-navbar .nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white !important;
  transform: translateY(-1px);
}

.hero-section {
  padding: 80px 20px;
  text-align: center;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%),
              url("https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3") center/cover;
  color: white;
  position: relative;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 20px;
  margin-bottom: 30px;
  opacity: 0.9;
  font-weight: 300;
}

.hero-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.hero-btn {
  padding: 14px 28px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.hero-btn.primary {
  background: white;
  color: #667eea;
}

.hero-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
  color: #5a67d8;
  text-decoration: none;
}

.hero-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.hero-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
}

.books-section {
  padding: 60px 20px;
  background: white;
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 12px;
}

.section-subtitle {
  font-size: 18px;
  color: #718096;
  font-weight: 300;
}

.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.book-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.book-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.book-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.book-card:hover .book-image {
  transform: scale(1.05);
}

.book-content {
  padding: 24px;
}

.book-title {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
  line-height: 1.3;
}

.book-author {
  font-size: 14px;
  color: #718096;
  margin-bottom: 12px;
}

.book-genre {
  display: inline-block;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 12px;
}

.book-price {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 16px;
}

.book-actions {
  display: flex;
  gap: 8px;
}

.book-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.book-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.book-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.book-btn.secondary {
  background: #f8fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.book-btn.secondary:hover {
  background: #edf2f7;
  transform: translateY(-1px);
}

/* Loading States */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #4a5568;
}

.empty-state-description {
  font-size: 16px;
  margin-bottom: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 36px;
  }

  .hero-subtitle {
    font-size: 18px;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .section-title {
    font-size: 28px;
  }

  .books-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }

  .book-content {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 60px 15px;
  }

  .hero-title {
    font-size: 28px;
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .books-grid {
    grid-template-columns: 1fr;
  }

  .books-section {
    padding: 40px 15px;
  }
}