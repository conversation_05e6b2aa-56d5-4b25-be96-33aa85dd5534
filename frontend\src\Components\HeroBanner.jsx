import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FaBook, FaSearch, FaStar, FaArrowRight } from 'react-icons/fa';

const HeroBanner = () => {
  const navigate = useNavigate();

  const handleExploreClick = () => {
    navigate('/books');
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    const searchTerm = e.target.search.value;
    if (searchTerm.trim()) {
      navigate(`/books?search=${encodeURIComponent(searchTerm)}`);
    }
  };

  return (
    <section className="hero-banner">
      <div className="hero-content container mx-auto px-4 py-16 flex flex-col items-center justify-center text-center">
        {/* Main Heading */}
        <div className="mb-8 fade-in">
          <h1 className="text-5xl md:text-7xl font-bold mb-4 font-serif-primary">
            Discover Your Next
            <span className="block text-amber-300">Great Read</span>
          </h1>
          <p className="text-xl md:text-2xl font-serif-secondary opacity-90 max-w-2xl mx-auto">
            Immerse yourself in a world of knowledge, adventure, and imagination. 
            From timeless classics to contemporary masterpieces.
          </p>
        </div>

        {/* Search Bar */}
        <div className="w-full max-w-2xl mb-8 slide-up">
          <form onSubmit={handleSearchSubmit} className="relative">
            <input
              type="text"
              name="search"
              placeholder="Search for books, authors, or genres..."
              className="w-full pl-12 pr-16 py-4 text-lg border-2 border-amber-300 rounded-full
                        focus:ring-4 focus:ring-amber-500/30 focus:border-amber-400
                        bg-white/90 backdrop-blur-sm text-amber-900 placeholder-amber-700
                        font-sans transition-all duration-300"
            />
            <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 
                              text-amber-600 text-xl" />
            <button
              type="submit"
              className="absolute right-2 top-1/2 transform -translate-y-1/2
                        bg-gradient-to-r from-amber-600 to-orange-600 text-white
                        px-6 py-2 rounded-full hover:from-amber-700 hover:to-orange-700
                        transition-all duration-300 font-semibold"
            >
              Search
            </button>
          </form>
        </div>

        {/* Stats */}
        <div className="flex flex-wrap justify-center gap-8 mb-8 slide-up">
          <div className="text-center">
            <div className="text-3xl font-bold text-amber-300 font-serif-primary">10K+</div>
            <div className="text-sm opacity-80 font-sans">Books Available</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-amber-300 font-serif-primary">5K+</div>
            <div className="text-sm opacity-80 font-sans">Happy Readers</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center text-3xl font-bold text-amber-300">
              <span className="font-serif-primary">4.8</span>
              <FaStar className="ml-1 text-yellow-400" />
            </div>
            <div className="text-sm opacity-80 font-sans">Average Rating</div>
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 slide-up">
          <button
            onClick={handleExploreClick}
            className="bg-gradient-to-r from-amber-600 to-orange-600 text-white
                      px-8 py-4 rounded-full text-lg font-semibold
                      hover:from-amber-700 hover:to-orange-700
                      transform hover:scale-105 transition-all duration-300
                      shadow-lg hover:shadow-xl font-sans
                      flex items-center justify-center gap-2"
          >
            <FaBook />
            Explore Collection
            <FaArrowRight />
          </button>
          
          <button
            onClick={() => navigate('/bestsellers')}
            className="bg-transparent border-2 border-amber-300 text-amber-100
                      px-8 py-4 rounded-full text-lg font-semibold
                      hover:bg-amber-300 hover:text-amber-900
                      transform hover:scale-105 transition-all duration-300
                      font-sans"
          >
            View Bestsellers
          </button>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-10 left-10 opacity-20 animate-pulse">
          <FaBook className="text-6xl text-amber-300" />
        </div>
        <div className="absolute bottom-10 right-10 opacity-20 animate-pulse delay-1000">
          <FaBook className="text-4xl text-amber-300" />
        </div>
        <div className="absolute top-1/3 right-20 opacity-10 animate-pulse delay-500">
          <FaBook className="text-8xl text-amber-300" />
        </div>
      </div>
    </section>
  );
};

export default HeroBanner;
