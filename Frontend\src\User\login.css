/* Login Page Styles */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

.login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-title {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.login-subtitle {
  color: #718096;
  font-size: 16px;
}

.demo-section {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
  text-align: center;
}

.demo-title {
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 12px;
}

.demo-buttons {
  display: flex;
  gap: 8px;
}

.demo-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.demo-btn.user {
  background: #4299e1;
  color: white;
}

.demo-btn.user:hover {
  background: #3182ce;
  transform: translateY(-1px);
}

.demo-btn.seller {
  background: #48bb78;
  color: white;
}

.demo-btn.seller:hover {
  background: #38a169;
  transform: translateY(-1px);
}

.user-type-section {
  margin-bottom: 20px;
}

.user-type-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8px;
}

.user-type-tabs {
  display: flex;
  background: #f7fafc;
  border-radius: 10px;
  padding: 4px;
  gap: 4px;
}

.user-type-tab {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  color: #718096;
}

.user-type-tab.active {
  background: white;
  color: #4299e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-type-tab:hover:not(.active) {
  color: #4a5568;
}

.form-group {
  margin-bottom: 20px;
}

.form-input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.2s ease;
  background: #fafafa;
}

.form-input:focus {
  outline: none;
  border-color: #4299e1;
  background: white;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.password-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: #718096;
  padding: 4px;
}

.password-toggle:hover {
  color: #4a5568;
}

.error-message {
  background: #fed7d7;
  color: #c53030;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  text-align: center;
  margin-bottom: 16px;
  border: 1px solid #feb2b2;
}

.login-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #4299e1 0%, #667eea 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(66, 153, 225, 0.3);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.signup-link {
  color: #718096;
  font-size: 14px;
}

.signup-button {
  background: none;
  border: none;
  color: #4299e1;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
}

.signup-button:hover {
  color: #3182ce;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-card {
    padding: 24px;
    margin: 10px;
  }
  
  .login-title {
    font-size: 24px;
  }
  
  .demo-buttons {
    flex-direction: column;
  }
  
  .user-type-tabs {
    flex-direction: column;
  }
}

/* Loading Animation */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Success Animation */
.success-checkmark {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #48bb78;
  position: relative;
  margin-right: 8px;
}

.success-checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}
