import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, <PERSON>ton, Badge, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { FaShoppingBag, FaCalendarAlt, FaTruck, FaEye, FaStar, FaArrowLeft } from 'react-icons/fa';
import Unavbar from './Unavbar';
import Footer from '../Components/Footer';

const MyOrdersNew = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);

      // Get orders from localStorage (for demo purposes)
      const storedOrders = JSON.parse(localStorage.getItem('userOrders') || '[]');

      // If no stored orders, create sample orders with proper 7-day delivery calculation
      if (storedOrders.length === 0) {
        const sampleOrders = [
          {
            _id: 'ORD001',
            booktitle: '1984',
            bookauthor: 'George Orwell',
            totalamount: 299,
            BookingDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toLocaleDateString(), // 5 days ago
            Delivery: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toLocaleDateString(), // 2 days from now (7 days total)
            itemImage: '1984 by George Orwell.jpeg',
            description: 'A dystopian social science fiction novel and cautionary tale.',
            category: 'Fiction',
            quantity: 1,
            status: 'Processing'
          },
          {
            _id: 'ORD002',
            booktitle: 'To Kill a Mockingbird',
            bookauthor: 'Harper Lee',
            totalamount: 349,
            BookingDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toLocaleDateString(), // 10 days ago
            Delivery: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toLocaleDateString(), // 3 days ago (delivered)
            itemImage: 'To Kill a Mockingbird by Harper Lee.jpeg',
            description: 'A novel about racial injustice and childhood in the American South.',
            category: 'Classic',
            quantity: 1,
            status: 'Delivered'
          },
          {
            _id: 'ORD003',
            booktitle: 'The Great Gatsby',
            bookauthor: 'F. Scott Fitzgerald',
            totalamount: 279,
            BookingDate: new Date().toLocaleDateString(), // Today
            Delivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(), // 7 days from now
            itemImage: 'The Great Gatsby by F. Scott Fitzgerald.jpeg',
            description: 'A classic American novel set in the Jazz Age.',
            category: 'Classic',
            quantity: 2,
            status: 'Confirmed'
          }
        ];

        localStorage.setItem('userOrders', JSON.stringify(sampleOrders));
        setOrders(sampleOrders);
      } else {
        setOrders(storedOrders);
      }

      setError(null);
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError('Failed to load orders. Please try again later.');
    } finally {
      setLoading(false);
    }
  };



  const getOrderStatus = (bookingDate, deliveryDate, orderStatus) => {
    const today = new Date();
    const booking = new Date(bookingDate);
    const delivery = new Date(deliveryDate);

    // Use the stored status if available, otherwise calculate based on dates
    if (orderStatus) {
      const statusMap = {
        'Confirmed': { status: 'Confirmed', color: 'primary', icon: FaCalendarAlt },
        'Processing': { status: 'Processing', color: 'info', icon: FaTruck },
        'Shipped': { status: 'Shipped', color: 'warning', icon: FaTruck },
        'Delivered': { status: 'Delivered', color: 'success', icon: FaShoppingBag }
      };
      return statusMap[orderStatus] || { status: orderStatus, color: 'secondary', icon: FaShoppingBag };
    }

    if (today < booking) {
      return { status: 'Pending', color: 'warning', icon: FaCalendarAlt };
    } else if (today >= booking && today < delivery) {
      return { status: 'Processing', color: 'info', icon: FaTruck };
    } else {
      return { status: 'Delivered', color: 'success', icon: FaShoppingBag };
    }
  };

  const getImageUrl = (imageName) => {
    if (imageName && imageName.includes('.jpeg')) {
      return `/${imageName}`;
    }
    return `/${imageName}.jpeg`;
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #2c1810 0%, #8b4513 100%)' }}>
        <Unavbar />
        <Container className="mt-5">
          <div className="text-center">
            <Spinner animation="border" variant="light" />
            <p className="mt-3 text-white">Loading your orders...</p>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)' }}>
      <Unavbar />

      <Container className="py-5">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center">
                <FaShoppingBag className="me-3" size={32} style={{ color: '#FF6600' }} />
                <h1 className="mb-0" style={{ fontFamily: 'Segoe UI, sans-serif', color: '#333', fontWeight: '600' }}>
                  My Orders
                </h1>
              </div>
              <Button
                onClick={() => navigate('/books')}
                className="d-flex align-items-center"
                style={{
                  backgroundColor: '#FF6600',
                  borderColor: '#FF6600',
                  color: 'white',
                  fontFamily: 'Segoe UI, sans-serif',
                  fontWeight: '600',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '8px 16px'
                }}
                onMouseOver={(e) => e.target.style.backgroundColor = '#e55a00'}
                onMouseOut={(e) => e.target.style.backgroundColor = '#FF6600'}
              >
                <FaArrowLeft className="me-2" />
                Continue Shopping
              </Button>
            </div>
          </Col>
        </Row>

        {error && (
          <Alert variant="warning" className="text-center mb-4">
            {error}
          </Alert>
        )}

        {orders.length === 0 ? (
          <Row className="justify-content-center">
            <Col md={6}>
              <Card className="text-center shadow-lg" style={{ background: 'rgba(255,255,255,0.95)' }}>
                <Card.Body className="py-5">
                  <FaShoppingBag size={64} className="text-muted mb-3" />
                  <h4 className="text-dark mb-3">No orders found</h4>
                  <p className="text-muted mb-4">You haven't placed any orders yet!</p>
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => navigate('/books')}
                    style={{ background: '#8b4513', borderColor: '#8b4513' }}
                  >
                    Start Shopping
                  </Button>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        ) : (
          <Row>
            {orders.map(order => {
              const orderStatus = getOrderStatus(order.BookingDate, order.Delivery, order.status);
              const StatusIcon = orderStatus.icon;

              return (
                <Col key={order._id} xs={12} className="mb-4">
                  <Card className="shadow-lg" style={{ background: 'rgba(255,255,255,0.95)' }}>
                    <Card.Header style={{ background: '#8b4513', color: 'white' }}>
                      <Row className="align-items-center">
                        <Col>
                          <h6 className="mb-0">Order #{order._id}</h6>
                          <small>Placed on {formatDate(order.BookingDate)}</small>
                        </Col>
                        <Col xs="auto">
                          <Badge bg={orderStatus.color} className="d-flex align-items-center">
                            <StatusIcon className="me-1" />
                            {orderStatus.status}
                          </Badge>
                        </Col>
                      </Row>
                    </Card.Header>
                    <Card.Body>
                      <Row className="align-items-center">
                        <Col md={2}>
                          <img
                            src={getImageUrl(order.itemImage || order.booktitle)}
                            className="img-fluid rounded"
                            alt={order.booktitle}
                            style={{ height: '120px', width: '90px', objectFit: 'cover' }}
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = '/default_cover.svg';
                            }}
                          />
                        </Col>
                        <Col md={6}>
                          <h5 style={{ color: '#2c1810', fontFamily: 'Playfair Display, serif' }}>
                            {order.booktitle}
                          </h5>
                          <p className="text-muted mb-1">by {order.bookauthor}</p>
                          <p className="text-muted mb-2">{order.description}</p>
                          <div className="d-flex gap-3">
                            <Badge bg="secondary">{order.category}</Badge>
                            <small className="text-muted">Qty: {order.quantity}</small>
                          </div>
                        </Col>
                        <Col md={4}>
                          <div className="text-end">
                            <h4 className="mb-3" style={{ color: '#8b4513' }}>₹{order.totalamount}</h4>

                            <div className="mb-3">
                              <div className="d-flex justify-content-between mb-1">
                                <small className="text-muted">Order Date:</small>
                                <small><strong>{formatDate(order.BookingDate)}</strong></small>
                              </div>
                              <div className="d-flex justify-content-between mb-1">
                                <small className="text-muted">Expected Delivery:</small>
                                <small><strong>{formatDate(order.Delivery)}</strong></small>
                              </div>
                            </div>

                            <div className="d-flex gap-2 justify-content-end">
                              <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={() => navigate(`/orderitem/${order._id}`)}
                              >
                                <FaEye className="me-1" />
                                View Details
                              </Button>
                              {orderStatus.status === 'Delivered' && (
                                <Button
                                  variant="outline-warning"
                                  size="sm"
                                  onClick={() => alert('Review functionality coming soon!')}
                                >
                                  <FaStar className="me-1" />
                                  Review
                                </Button>
                              )}
                            </div>
                          </div>
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>
              );
            })}
          </Row>
        )}

        {orders.length > 0 && (
          <Row className="mt-4">
            <Col className="text-center">
              <p style={{ color: '#666', fontFamily: 'Segoe UI, sans-serif' }}>
                Showing {orders.length} order{orders.length !== 1 ? 's' : ''}
              </p>
            </Col>
          </Row>
        )}
      </Container>
      <Footer />
    </div>
  );
};

export default MyOrdersNew;
