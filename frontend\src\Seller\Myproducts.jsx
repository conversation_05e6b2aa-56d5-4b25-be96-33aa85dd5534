import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Snavbar from './Snavbar';
import { FaTrash } from "react-icons/fa";

// Book Store - Seller's My Products Page
function Myproducts() {
  const [books, setBooks] = useState([]);

  useEffect(() => {
    // Fetch books for the logged-in seller
    const user = JSON.parse(localStorage.getItem('user'));
    if (user) {
      axios
        .get(`https://booknest-backend-55yh.onrender.com/getitem/${user.id}`)
        .then((response) => {
          setBooks(response.data);
        })
        .catch((error) => {
          console.error('Error fetching books: ', error);
        });
    }
  }, []);

  const handleDelete = (bookId) => {
    if (window.confirm('Are you sure you want to delete this book?')) {
      axios.delete(`https://booknest-backend-55yh.onrender.com/itemdelete/${bookId}`)
        .then(() => {
          setBooks(books.filter(book => book._id !== bookId));
        })
        .catch((error) => {
          alert('Failed to delete book.');
        });
    }
  };

  return (
    <div>
      <Snavbar />
      <div className="container mx-auto p-8">
        <h2 className="text-3xl font-semibold mb-4 text-center">My Books</h2>
        <div className="flex justify-end mb-6">
          <a
            href="/addbook"
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
          >
            + Add New Book
          </a>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {books.length === 0 && (
            <div className="col-span-full text-center text-gray-500">
              No books found. Click "Add New Book" to list your first book!
            </div>
          )}
          {books.map((book) => (
            <div key={book._id} className="bg-white p-4 rounded shadow flex flex-col">
              <div className="flex justify-end">
                <button
                  onClick={() => handleDelete(book._id)}
                  className="text-red-600 hover:text-red-800"
                  title="Delete Book"
                >
                  <FaTrash />
                </button>
              </div>
              <img
                src={book.itemImage?.startsWith('book-covers/')
                  ? `/${book.itemImage}`
                  : `https://booknest-backend-55yh.onrender.com/${book.itemImage}`}
                alt={book.title}
                className="rounded-t-lg object-cover mb-4"
                style={{ height: "300px", width: "100%", objectFit: "cover" }}
                onError={e => { e.target.src = "/book-covers/default.jpg"; }}
              />
              <div className="flex-1 flex flex-col">
                <p className="text-xl font-bold mb-2">{book.title}</p>
                <p className="text-gray-700 mb-1">Author: {book.author}</p>
                <p className="text-gray-700 mb-1">Genre: {book.genre}</p>
                <p className="text-blue-500 font-bold mb-1">Price: ${book.price}</p>
                <p className="text-gray-600 mb-2">
                  <strong>Description:</strong> {book.description?.slice(0, 150)}{book.description && book.description.length > 150 ? '...' : ''}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default Myproducts;
