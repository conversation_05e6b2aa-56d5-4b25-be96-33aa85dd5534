// src/components/Snavbar.jsx

import React from 'react';
import { Navbar, Nav, Container, Button } from 'react-bootstrap';
import { Link, useNavigate } from "react-router-dom";

const Snavbar = () => {
  const navigate = useNavigate();
  const user = localStorage.getItem('user');
  let userName = '';
  if (user) {
    try {
      userName = JSON.parse(user).name;
    } catch (e) {
      userName = '';
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/best-wishes');
  };

  return (
    <Navbar expand="lg" style={{
      background: 'linear-gradient(135deg, #FF6600 0%, #FF9900 100%)',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      padding: '0.75rem 0'
    }}>
      <Container>
        <Navbar.Brand>
          <Link to='/shome' style={{
            color: "white",
            textDecoration: "none",
            fontWeight: "700",
            fontSize: "1.5rem",
            fontFamily: 'Segoe UI, sans-serif',
            display: 'flex',
            alignItems: 'center'
          }}>
            <img
              src="/bookstore-logo.png"
              alt="BookStore Logo"
              style={{ width: "40px", marginRight: "12px", verticalAlign: "middle" }}
              onError={e => { e.target.style.display = 'none'; }}
            />
            📚 BookNest
            <span style={{
              fontSize: "0.9rem",
              fontWeight: "500",
              marginLeft: "8px",
              background: 'rgba(255,255,255,0.2)',
              padding: '4px 8px',
              borderRadius: '12px'
            }}>
              Seller
            </span>
          </Link>
        </Navbar.Brand>
        <Navbar.Toggle aria-controls="basic-navbar-nav" style={{ border: 'none' }} />
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="ms-auto" style={{ alignItems: "center" }}>
            <Link to="/shome" style={{
              padding: "12px 16px",
              color: "white",
              textDecoration: "none",
              fontFamily: 'Segoe UI, sans-serif',
              fontWeight: '600',
              borderRadius: '8px',
              transition: 'all 0.2s ease-in-out',
              margin: '0 4px'
            }}
            onMouseEnter={(e) => e.target.style.background = 'rgba(255,255,255,0.2)'}
            onMouseLeave={(e) => e.target.style.background = 'transparent'}>
              🏠 Dashboard
            </Link>
            <Link to="/myproducts" style={{
              padding: "12px 16px",
              color: "white",
              textDecoration: "none",
              fontFamily: 'Segoe UI, sans-serif',
              fontWeight: '600',
              borderRadius: '8px',
              transition: 'all 0.2s ease-in-out',
              margin: '0 4px'
            }}
            onMouseEnter={(e) => e.target.style.background = 'rgba(255,255,255,0.2)'}
            onMouseLeave={(e) => e.target.style.background = 'transparent'}>
              📚 My Books
            </Link>
            <Link to="/addbook" style={{
              padding: "12px 16px",
              color: "white",
              textDecoration: "none",
              fontFamily: 'Segoe UI, sans-serif',
              fontWeight: '600',
              borderRadius: '8px',
              transition: 'all 0.2s ease-in-out',
              margin: '0 4px'
            }}
            onMouseEnter={(e) => e.target.style.background = 'rgba(255,255,255,0.2)'}
            onMouseLeave={(e) => e.target.style.background = 'transparent'}>
              ➕ Add Book
            </Link>
            <Link to="/orders" style={{
              padding: "12px 16px",
              color: "white",
              textDecoration: "none",
              fontFamily: 'Segoe UI, sans-serif',
              fontWeight: '600',
              borderRadius: '8px',
              transition: 'all 0.2s ease-in-out',
              margin: '0 4px'
            }}
            onMouseEnter={(e) => e.target.style.background = 'rgba(255,255,255,0.2)'}
            onMouseLeave={(e) => e.target.style.background = 'transparent'}>
              🛒 Orders
            </Link>
            <Button
              style={{
                background: 'rgba(255,255,255,0.2)',
                border: '2px solid rgba(255,255,255,0.3)',
                color: 'white',
                fontFamily: 'Segoe UI, sans-serif',
                fontWeight: '600',
                borderRadius: '8px',
                marginLeft: '15px',
                transition: 'all 0.2s ease-in-out'
              }}
              size="sm"
              onClick={handleLogout}
              onMouseEnter={(e) => {
                e.target.style.background = 'rgba(255,255,255,0.3)';
                e.target.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'rgba(255,255,255,0.2)';
                e.target.style.transform = 'translateY(0px)';
              }}
            >
              🚪 Logout
            </Button>
            {userName && (
              <span style={{
                color: "white",
                marginLeft: "15px",
                fontWeight: "600",
                fontFamily: 'Segoe UI, sans-serif',
                background: 'rgba(255,255,255,0.2)',
                padding: '6px 12px',
                borderRadius: '20px',
                fontSize: '0.9rem'
              }}>
                👤 {userName}
              </span>
            )}
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Snavbar;
