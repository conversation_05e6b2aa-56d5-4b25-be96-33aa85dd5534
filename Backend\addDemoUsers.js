const mongoose = require('mongoose');
require('./db/config');
const users = require('./db/Users/<USER>');
const seller = require('./db/Seller/Sellers');
const Admin = require('./db/Admin/Admin');

// Demo users for testing
const demoUsers = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123'
  },
  {
    name: 'Demo User',
    email: '<EMAIL>',
    password: 'demo123'
  }
];

// Demo sellers
const demoSellers = [
  {
    name: 'BookStore Seller',
    email: '<EMAIL>',
    password: 'seller123'
  },
  {
    name: 'Classic Books Seller',
    email: '<EMAIL>',
    password: 'classic123'
  }
];

// Demo admin
const demoAdmin = [
  {
    name: 'BookStore Admin',
    email: '<EMAIL>',
    password: 'admin123'
  }
];

async function addDemoUsers() {
  try {
    console.log('👥 Adding demo users...');
    
    // Add demo users
    console.log('\n📝 Adding regular users...');
    for (const user of demoUsers) {
      const existingUser = await users.findOne({ email: user.email });
      if (!existingUser) {
        await users.create(user);
        console.log(`✅ Added user: ${user.name} (${user.email})`);
      } else {
        console.log(`⚠️  User already exists: ${user.email}`);
      }
    }
    
    // Add demo sellers
    console.log('\n🏪 Adding sellers...');
    for (const sellerData of demoSellers) {
      const existingSeller = await seller.findOne({ email: sellerData.email });
      if (!existingSeller) {
        await seller.create(sellerData);
        console.log(`✅ Added seller: ${sellerData.name} (${sellerData.email})`);
      } else {
        console.log(`⚠️  Seller already exists: ${sellerData.email}`);
      }
    }
    
    // Add demo admin
    console.log('\n👑 Adding admin...');
    for (const adminData of demoAdmin) {
      const existingAdmin = await Admin.findOne({ email: adminData.email });
      if (!existingAdmin) {
        await Admin.create(adminData);
        console.log(`✅ Added admin: ${adminData.name} (${adminData.email})`);
      } else {
        console.log(`⚠️  Admin already exists: ${adminData.email}`);
      }
    }
    
    console.log('\n🎉 Demo users setup completed!');
    console.log('\n📋 Login Credentials:');
    console.log('=' .repeat(50));
    console.log('👤 Regular Users:');
    demoUsers.forEach(user => {
      console.log(`   Email: ${user.email} | Password: ${user.password}`);
    });
    console.log('\n🏪 Sellers:');
    demoSellers.forEach(seller => {
      console.log(`   Email: ${seller.email} | Password: ${seller.password}`);
    });
    console.log('\n👑 Admin:');
    demoAdmin.forEach(admin => {
      console.log(`   Email: ${admin.email} | Password: ${admin.password}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error adding demo users:', error);
    process.exit(1);
  }
}

addDemoUsers();
