const mongoose = require('mongoose');
require('./db/config');
const items = require('./db/Seller/Additem');

// Mapping of book filenames to correct book data (based on actual book names in public folder)
const correctBookData = {
  "1984 by <PERSON>.jpeg": {
    title: "1984",
    author: "<PERSON>",
    genre: "Dystopian Fiction",
    description: "A dystopian social science fiction novel about totalitarian control and surveillance.",
    price: "279"
  },
  "A Thousand Splendid Suns.jpeg": {
    title: "A Thousand Splendid Suns",
    author: "<PERSON><PERSON><PERSON>",
    genre: "Historical Fiction",
    description: "A powerful story of two women in Afghanistan whose lives become intertwined through friendship and survival.",
    price: "369"
  },
  "Atomic Habits.jpeg": {
    title: "Atomic Habits",
    author: "James Clear",
    genre: "Self-Help",
    description: "A practical guide to building good habits and breaking bad ones through small, incremental changes.",
    price: "449"
  },
  "Educated.jpeg": {
    title: "Educated",
    author: "Tara Westover",
    genre: "Memoir",
    description: "A powerful memoir about education, family, and the struggle between loyalty and independence.",
    price: "389"
  },
  "Life of Pi.jpeg": {
    title: "Life of Pi",
    author: "Yann <PERSON>",
    genre: "Adventure Fiction",
    description: "An extraordinary tale of survival and faith about a boy stranded on a lifeboat with a Bengal tiger.",
    price: "329"
  },
  "One Hundred Years of Solitude.jpeg": {
    title: "One Hundred Years of Solitude",
    author: "Gabriel García Márquez",
    genre: "Magical Realism",
    description: "A multi-generational saga of the Buendía family in the fictional town of Macondo.",
    price: "399"
  },
  "The Alchemist.jpeg": {
    title: "The Alchemist",
    author: "Paulo Coelho",
    genre: "Fiction",
    description: "A philosophical novel about a young shepherd's journey to find treasure and discover his personal legend.",
    price: "299"
  },
  "The Book Thief.jpeg": {
    title: "The Book Thief",
    author: "Markus Zusak",
    genre: "Historical Fiction",
    description: "A haunting tale narrated by Death about a young girl living in Nazi Germany who steals books.",
    price: "359"
  },
  "The Catcher in the Rye.jpeg": {
    title: "The Catcher in the Rye",
    author: "J.D. Salinger",
    genre: "Coming of Age",
    description: "A controversial novel about teenage rebellion and alienation in post-war America.",
    price: "289"
  },
  "The Da Vinci Code.jpeg": {
    title: "The Da Vinci Code",
    author: "Dan Brown",
    genre: "Mystery Thriller",
    description: "A gripping mystery that follows symbologist Robert Langdon as he uncovers ancient secrets.",
    price: "379"
  },
  "The Fault in Our Stars.jpeg": {
    title: "The Fault in Our Stars",
    author: "John Green",
    genre: "Young Adult Fiction",
    description: "A heart-wrenching love story between two teenagers who meet in a cancer support group.",
    price: "319"
  },
  "The Girl with the Dragon Tattoo.jpeg": {
    title: "The Girl with the Dragon Tattoo",
    author: "Stieg Larsson",
    genre: "Crime Thriller",
    description: "A gripping crime thriller about a journalist and a hacker investigating a wealthy family's dark secrets.",
    price: "449"
  },
  "The Great Gatsby.jpeg": {
    title: "The Great Gatsby",
    author: "F. Scott Fitzgerald",
    genre: "Classic Literature",
    description: "A classic American novel set in the Jazz Age, exploring themes of wealth, love, and the American Dream.",
    price: "299"
  },
  "The Hunger Games.jpeg": {
    title: "The Hunger Games",
    author: "Suzanne Collins",
    genre: "Dystopian Fiction",
    description: "A thrilling dystopian novel about a girl who volunteers to take her sister's place in a deadly competition.",
    price: "399"
  },
  "The Kite Runner.jpeg": {
    title: "The Kite Runner",
    author: "Khaled Hosseini",
    genre: "Historical Fiction",
    description: "A powerful story of friendship, betrayal, and redemption set against the backdrop of Afghanistan's tumultuous history.",
    price: "349"
  },
  "To Kill a Mockingbird.jpeg": {
    title: "To Kill a Mockingbird",
    author: "Harper Lee",
    genre: "Fiction",
    description: "A gripping tale of racial injustice and childhood innocence in the American South.",
    price: "339"
  }
  "1700632005607-136251[1].jpg": {
    title: "Life of Pi",
    author: "Yann Martel",
    genre: "Adventure Fiction",
    description: "An extraordinary tale of survival and faith about a boy stranded on a lifeboat with a Bengal tiger.",
    price: "279"
  },
  "1700632074778-136251.jpg": {
    title: "The Book Thief",
    author: "Markus Zusak",
    genre: "Historical Fiction",
    description: "A haunting tale narrated by Death about a young girl living in Nazi Germany who steals books.",
    price: "259"
  },
  "1700632515790-29502358[1].jpg": {
    title: "The Fault in Our Stars",
    author: "John Green",
    genre: "Young Adult Fiction",
    description: "A heart-wrenching love story between two teenagers who meet in a cancer support group.",
    price: "289"
  },
  "1700632736939-30186948[1].jpg": {
    title: "The Hunger Games",
    author: "Suzanne Collins",
    genre: "Dystopian Fiction",
    description: "A thrilling dystopian novel about a girl who volunteers to take her sister's place in a deadly competition.",
    price: "399"
  },
  "1700632928170-80830635[1].jpg": {
    title: "The Da Vinci Code",
    author: "Dan Brown",
    genre: "Mystery Thriller",
    description: "A gripping mystery that follows symbologist Robert Langdon as he investigates a murder in the Louvre.",
    price: "599"
  },
  "1700633112352-42983957[1].jpg": {
    title: "The Girl with the Dragon Tattoo",
    author: "Stieg Larsson",
    genre: "Crime Thriller",
    description: "A gripping crime thriller about a journalist and a hacker investigating a wealthy family's dark secrets.",
    price: "449"
  },
  "1700633869849-122765395[1].jpg": {
    title: "Gone Girl",
    author: "Gillian Flynn",
    genre: "Psychological Thriller",
    description: "A psychological thriller about a marriage gone terribly wrong when a wife disappears on her anniversary.",
    price: "379"
  },
  "1700638048715-61111298[1].jpg": {
    title: "The Silent Patient",
    author: "Alex Michaelides",
    genre: "Psychological Thriller",
    description: "A gripping psychological thriller about a woman who refuses to speak after allegedly murdering her husband.",
    price: "329"
  },
  "1700643491707-63331415[1].jpg": {
    title: "Where the Crawdads Sing",
    author: "Delia Owens",
    genre: "Mystery Fiction",
    description: "A coming-of-age mystery about a girl who grows up isolated in the marshes of North Carolina.",
    price: "359"
  },
  "1700721610720-29502358[1].jpg": {
    title: "The Seven Husbands of Evelyn Hugo",
    author: "Taylor Jenkins Reid",
    genre: "Historical Fiction",
    description: "A captivating novel about a reclusive Hollywood icon who finally decides to tell her life story.",
    price: "319"
  },
  "1742294964528-images.jpeg": {
    title: "Educated",
    author: "Tara Westover",
    genre: "Memoir",
    description: "A powerful memoir about education, family, and the struggle between loyalty and independence.",
    price: "389"
  }
};

async function updateBookData() {
  try {
    console.log('🔄 Starting book data update...');
    
    // Get all books from database
    const books = await items.find();
    console.log(`📚 Found ${books.length} books in database`);
    
    let updatedCount = 0;
    
    for (const book of books) {
      const correctData = correctBookData[book.itemImage];
      
      if (correctData) {
        // Update the book with correct data
        await items.findByIdAndUpdate(book._id, {
          title: correctData.title,
          author: correctData.author,
          genre: correctData.genre,
          description: correctData.description,
          price: correctData.price
        });
        
        console.log(`✅ Updated: ${book.itemImage} -> ${correctData.title} by ${correctData.author}`);
        updatedCount++;
      } else {
        console.log(`⚠️  No mapping found for: ${book.itemImage}`);
      }
    }
    
    console.log(`\n🎉 Update complete! Updated ${updatedCount} out of ${books.length} books.`);
    
    // Display updated books
    console.log('\n📖 Updated book list:');
    const updatedBooks = await items.find();
    updatedBooks.forEach(book => {
      console.log(`- ${book.title} by ${book.author} (${book.itemImage})`);
    });
    
  } catch (error) {
    console.error('❌ Error updating book data:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the update
updateBookData();
