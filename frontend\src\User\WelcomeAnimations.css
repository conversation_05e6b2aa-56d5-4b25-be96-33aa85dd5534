/* Interactive Welcome Page Animations */

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(20, 184, 166, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(20, 184, 166, 0.6);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-fade-in {
  animation: fade-in 1s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.8s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.shimmer-effect {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Hover effects */
.card-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-effect:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Interactive button effects */
.interactive-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.interactive-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.interactive-button:hover::before {
  left: 100%;
}

/* Progress bar animation */
.progress-bar {
  background: linear-gradient(90deg, #14b8a6, #3b82f6, #8b5cf6, #ef4444);
  background-size: 400% 100%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Floating elements */
.floating-element {
  animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(2) {
  animation-delay: -2s;
}

.floating-element:nth-child(3) {
  animation-delay: -4s;
}

/* Glowing text effect */
.glow-text {
  text-shadow: 0 0 10px rgba(20, 184, 166, 0.5);
  transition: text-shadow 0.3s ease;
}

.glow-text:hover {
  text-shadow: 0 0 20px rgba(20, 184, 166, 0.8);
}

/* Interactive stats counter */
.stats-counter {
  transition: all 0.3s ease;
}

.stats-counter:hover {
  transform: scale(1.1);
  color: #14b8a6;
}

/* Book card flip effect */
.book-card {
  perspective: 1000px;
}

.book-card-inner {
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.book-card:hover .book-card-inner {
  transform: rotateY(180deg);
}

.book-card-front,
.book-card-back {
  backface-visibility: hidden;
}

.book-card-back {
  transform: rotateY(180deg);
}

/* Responsive animations */
@media (max-width: 768px) {
  .animate-fade-in,
  .animate-slide-in-left,
  .animate-slide-in-right {
    animation-duration: 0.5s;
  }
  
  .card-hover-effect:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .glow-text {
    text-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
  }
  
  .glow-text:hover {
    text-shadow: 0 0 20px rgba(34, 197, 94, 0.8);
  }
}
