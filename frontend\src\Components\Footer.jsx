import React from 'react';
import { FaFacebook, FaInstagram, FaTwitter } from 'react-icons/fa';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  return (
    <footer
      style={{
        width: '100%',
        background: 'linear-gradient(120deg, #232526 0%, #414345 100%)',
        color: '#fff',
        padding: '0',
        margin: '0',
        position: 'relative',
        left: 0,
        bottom: 0,
        zIndex: 1000,
        fontFamily: "'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
      }}
    >
      <div
        style={{
          maxWidth: 1200,
          margin: '0 auto',
          padding: '0',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            padding: '32px 0 16px 0',
          }}
        >
          <div
            style={{
              fontWeight: 700,
              fontSize: '2rem',
              letterSpacing: '2px',
              color: '#ffd700',
              marginBottom: 8,
              textShadow: '0 2px 8px #0002',
            }}
          >
            BOOKNEST BOOKSTORE
          </div>
          <div
            style={{
              fontSize: '1.1rem',
              color: '#fff',
              marginBottom: 16,
              fontStyle: 'italic',
              textAlign: 'center',
              maxWidth: 600,
            }}
          >
            Your one-stop online book store for bestsellers, classics, and new releases. Discover your next great read today!
          </div>
          <div style={{ marginBottom: 16 }}>
            <a href="/contact" style={{ textDecoration: 'none' }}>
              <button
                style={{
                  height: "44px",
                  minWidth: "140px",
                  background: "#ffd700",
                  color: "#232526",
                  border: "none",
                  borderRadius: "6px",
                  fontWeight: 600,
                  fontSize: "16px",
                  cursor: "pointer",
                  boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
                  padding: '0 24px'
                }}
              >
                Contact Us
              </button>
            </a>
          </div>
          <div style={{ marginBottom: 12, fontSize: 16 }}>
            <span style={{ fontWeight: 500 }}>Call:</span>{' '}
            <a href="tel:127-865-586-67" style={{ color: "#ffd700", textDecoration: "none", fontWeight: 600 }}>
              127-865-586-67
            </a>
          </div>
          <div style={{ marginBottom: 16 }}>
            <a href="#" style={{ color: '#fff', margin: '0 8px', fontSize: 22 }} aria-label="Facebook"><FaFacebook /></a>
            <a href="#" style={{ color: '#fff', margin: '0 8px', fontSize: 22 }} aria-label="Instagram"><FaInstagram /></a>
            <a href="#" style={{ color: '#fff', margin: '0 8px', fontSize: 22 }} aria-label="Twitter"><FaTwitter /></a>
          </div>
          <div style={{ borderTop: '1px solid #444', width: '100%', margin: '16px 0 0 0' }}></div>
          <div style={{ color: "#bbb", margin: '12px 0 0 0', fontSize: "15px", textAlign: 'center' }}>
            Copyright &copy; {currentYear} By <span style={{ color: "#ffd700" }}>BOOKNEST BOOKSTORE</span>.
            <br />
            All Rights Reserved.
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
