const mongoose = require('mongoose');
require('./db/config');
const items = require('./db/Seller/Additem');

// Book data based on images in frontend/public folder
const booksFromPublic = [
  {
    title: "1984",
    author: "<PERSON>",
    genre: "Dystopian Fiction",
    description: "A dystopian social science fiction novel about totalitarian control and surveillance.",
    price: "279",
    itemImage: "1984 by <PERSON>.jpe<PERSON>",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "A Thousand Splendid Suns",
    author: "<PERSON><PERSON><PERSON>",
    genre: "Historical Fiction",
    description: "A powerful story of two women in Afghanistan whose lives become intertwined through friendship and survival.",
    price: "369",
    itemImage: "A Thousand Splendid Suns.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "Atomic Habits",
    author: "James Clear",
    genre: "Self-Help",
    description: "A practical guide to building good habits and breaking bad ones through small, incremental changes.",
    price: "449",
    itemImage: "Atomic Habits.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "Educated",
    author: "Tara Westover",
    genre: "Memoir",
    description: "A powerful memoir about education, family, and the struggle between loyalty and independence.",
    price: "389",
    itemImage: "Educated.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "Life of Pi",
    author: "Yann Martel",
    genre: "Adventure Fiction",
    description: "An extraordinary tale of survival and faith about a boy stranded on a lifeboat with a Bengal tiger.",
    price: "329",
    itemImage: "Life of Pi.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "One Hundred Years of Solitude",
    author: "Gabriel García Márquez",
    genre: "Magical Realism",
    description: "A multi-generational saga of the Buendía family in the fictional town of Macondo.",
    price: "399",
    itemImage: "One Hundred Years of Solitude.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "The Alchemist",
    author: "Paulo Coelho",
    genre: "Fiction",
    description: "A philosophical novel about a young shepherd's journey to find treasure and discover his personal legend.",
    price: "299",
    itemImage: "The Alchemist.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "The Book Thief",
    author: "Markus Zusak",
    genre: "Historical Fiction",
    description: "A haunting tale narrated by Death about a young girl living in Nazi Germany who steals books.",
    price: "359",
    itemImage: "The Book Thief.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "The Catcher in the Rye",
    author: "J.D. Salinger",
    genre: "Coming of Age",
    description: "A controversial novel about teenage rebellion and alienation in post-war America.",
    price: "289",
    itemImage: "The Catcher in the Rye.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "The Da Vinci Code",
    author: "Dan Brown",
    genre: "Mystery Thriller",
    description: "A gripping mystery that follows symbologist Robert Langdon as he uncovers ancient secrets.",
    price: "379",
    itemImage: "The Da Vinci Code.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "The Fault in Our Stars",
    author: "John Green",
    genre: "Young Adult Fiction",
    description: "A heart-wrenching love story between two teenagers who meet in a cancer support group.",
    price: "319",
    itemImage: "The Fault in Our Stars.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "The Girl with the Dragon Tattoo",
    author: "Stieg Larsson",
    genre: "Crime Thriller",
    description: "A gripping crime thriller about a journalist and a hacker investigating a wealthy family's dark secrets.",
    price: "449",
    itemImage: "The Girl with the Dragon Tattoo.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "The Great Gatsby",
    author: "F. Scott Fitzgerald",
    genre: "Classic Literature",
    description: "A classic American novel set in the Jazz Age, exploring themes of wealth, love, and the American Dream.",
    price: "299",
    itemImage: "The Great Gatsby.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "The Hunger Games",
    author: "Suzanne Collins",
    genre: "Dystopian Fiction",
    description: "A thrilling dystopian novel about a girl who volunteers to take her sister's place in a deadly competition.",
    price: "399",
    itemImage: "The Hunger Games.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "The Kite Runner",
    author: "Khaled Hosseini",
    genre: "Historical Fiction",
    description: "A powerful story of friendship, betrayal, and redemption set against the backdrop of Afghanistan's tumultuous history.",
    price: "349",
    itemImage: "The Kite Runner.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  },
  {
    title: "To Kill a Mockingbird",
    author: "Harper Lee",
    genre: "Fiction",
    description: "A gripping tale of racial injustice and childhood innocence in the American South.",
    price: "339",
    itemImage: "To Kill a Mockingbird.jpeg",
    userId: new mongoose.Types.ObjectId(),
    userName: "BookStore Admin"
  }
];

async function addBooksFromPublic() {
  try {
    console.log('📚 Adding books from public folder...');
    
    // Clear existing books
    await items.deleteMany({});
    console.log('🗑️  Cleared existing books');
    
    // Add books from public folder
    const result = await items.insertMany(booksFromPublic);
    console.log(`✅ Added ${result.length} books successfully!`);
    
    console.log('\n📖 Book Catalog:');
    console.log('=' .repeat(80));
    
    result.forEach((book, index) => {
      console.log(`${index + 1}. ${book.title}`);
      console.log(`   Author: ${book.author}`);
      console.log(`   Genre: ${book.genre}`);
      console.log(`   Price: ₹${book.price}`);
      console.log(`   Image: ${book.itemImage}`);
      console.log('   ' + '-'.repeat(70));
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error adding books:', error);
    process.exit(1);
  }
}

addBooksFromPublic();
