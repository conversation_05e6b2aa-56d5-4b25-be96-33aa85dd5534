import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FaBook, 
  FaFlask, 
  FaPalette, 
  FaGraduationCap, 
  FaStar, 
  FaGift,
  FaHeart,
  FaRocket,
  FaHistory,
  FaTheaterMasks,
  FaGlobe,
  FaChild
} from 'react-icons/fa';

const CategoriesNav = () => {
  const navigate = useNavigate();

  const categories = [
    {
      id: 'fiction',
      name: 'Fiction & Literature',
      icon: <FaBook />,
      color: 'from-amber-600 to-orange-600'
    },
    {
      id: 'science',
      name: 'Science & Technology',
      icon: <FaFlask />,
      color: 'from-blue-600 to-indigo-600'
    },
    {
      id: 'arts',
      name: 'Arts & Design',
      icon: <FaPalette />,
      color: 'from-purple-600 to-pink-600'
    },
    {
      id: 'academic',
      name: 'Academic & Reference',
      icon: <FaGraduationCap />,
      color: 'from-green-600 to-teal-600'
    },
    {
      id: 'bestsellers',
      name: 'Bestsellers',
      icon: <FaStar />,
      color: 'from-yellow-600 to-orange-600'
    },
    {
      id: 'romance',
      name: 'Romance',
      icon: <FaHeart />,
      color: 'from-red-600 to-pink-600'
    },
    {
      id: 'scifi',
      name: 'Sci-Fi & Fantasy',
      icon: <FaRocket />,
      color: 'from-indigo-600 to-purple-600'
    },
    {
      id: 'history',
      name: 'History & Biography',
      icon: <FaHistory />,
      color: 'from-gray-600 to-gray-700'
    },
    {
      id: 'mystery',
      name: 'Mystery & Thriller',
      icon: <FaTheaterMasks />,
      color: 'from-gray-800 to-black'
    },
    {
      id: 'travel',
      name: 'Travel & Culture',
      icon: <FaGlobe />,
      color: 'from-cyan-600 to-blue-600'
    },
    {
      id: 'children',
      name: 'Children\'s Books',
      icon: <FaChild />,
      color: 'from-pink-500 to-rose-500'
    },
    {
      id: 'gifts',
      name: 'Gift Ideas',
      icon: <FaGift />,
      color: 'from-emerald-600 to-green-600'
    }
  ];

  const handleCategoryClick = (categoryId) => {
    navigate(`/books?category=${categoryId}`);
  };

  return (
    <nav className="categories-nav">
      <div className="container mx-auto px-4">
        <div className="flex justify-center">
          <div className="flex space-x-4 overflow-x-auto pb-2 scrollbar-hide max-w-full">
            {categories.map((category, index) => (
              <button
                key={category.id}
                onClick={() => handleCategoryClick(category.id)}
                className="category-btn flex items-center space-x-2 fade-in"
                style={{ animationDelay: `${index * 0.05}s` }}
              >
                <span className="text-lg">{category.icon}</span>
                <span className="whitespace-nowrap">{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Custom scrollbar styles */}
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </nav>
  );
};

export default CategoriesNav;
