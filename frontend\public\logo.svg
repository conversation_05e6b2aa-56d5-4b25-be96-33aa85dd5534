<svg width="200" height="80" viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B4513;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#D2691E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#CD853F;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2C1810;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B4513;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Book Stack -->
  <g transform="translate(10, 15)">
    <!-- Book 1 (bottom) -->
    <rect x="0" y="35" width="35" height="8" fill="#8B4513" rx="1"/>
    <rect x="2" y="37" width="31" height="4" fill="#D2691E" rx="0.5"/>
    
    <!-- Book 2 (middle) -->
    <rect x="2" y="25" width="33" height="8" fill="#CD853F" rx="1"/>
    <rect x="4" y="27" width="29" height="4" fill="#DEB887" rx="0.5"/>
    
    <!-- Book 3 (top) -->
    <rect x="4" y="15" width="31" height="8" fill="#D2691E" rx="1"/>
    <rect x="6" y="17" width="27" height="4" fill="#F4A460" rx="0.5"/>
    
    <!-- Book spine details -->
    <line x1="8" y1="17" x2="8" y2="21" stroke="#8B4513" stroke-width="0.5"/>
    <line x1="29" y1="17" x2="29" y2="21" stroke="#8B4513" stroke-width="0.5"/>
    <line x1="6" y1="27" x2="6" y2="31" stroke="#8B4513" stroke-width="0.5"/>
    <line x1="31" y1="27" x2="31" y2="31" stroke="#8B4513" stroke-width="0.5"/>
  </g>
  
  <!-- Company Name -->
  <text x="55" y="35" font-family="serif" font-size="24" font-weight="bold" fill="url(#textGradient)">BookNest</text>
  <text x="55" y="52" font-family="serif" font-size="12" fill="#8B4513" opacity="0.8">Literary Haven</text>
  
  <!-- Decorative elements -->
  <circle cx="175" cy="25" r="3" fill="#D2691E" opacity="0.6"/>
  <circle cx="185" cy="35" r="2" fill="#CD853F" opacity="0.5"/>
  <circle cx="180" cy="45" r="2.5" fill="#8B4513" opacity="0.4"/>
</svg>
