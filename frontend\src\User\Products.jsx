import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>er, <PERSON>, Col, Card, Button, Badge, Form, InputGroup, Spinner, <PERSON><PERSON> } from 'react-bootstrap';
import { FaStar, FaStarHalfAlt, FaRegStar, FaShoppingCart, FaSearch, FaFilter } from 'react-icons/fa';
import axios from 'axios';
import Unavbar from './Unavbar';
import Footer from '../Components/Footer';

const BACKEND_URL = 'https://booknest-backend-55yh.onrender.com';

// All 16 books data with proper image names
const ALL_BOOKS = [
  { id: 1, title: '1984', author: '<PERSON>', price: 279, image: '1984 by <PERSON>.jpe<PERSON>', category: 'Fiction', rating: 4.5, reviews: 1250 },
  { id: 2, title: 'The Alchemist', author: '<PERSON>', price: 259, image: 'The Alchemist.jpeg', category: 'Philosophy', rating: 4.3, reviews: 980 },
  { id: 3, title: 'Atomic Habits', author: '<PERSON>', price: 399, image: 'Atomic Habits.jpeg', category: 'Self-Help', rating: 4.7, reviews: 2100 },
  { id: 4, title: 'The Great Gatsby', author: 'F. <PERSON> <PERSON>', price: 299, image: 'The Great Gatsby.jpeg', category: 'Classic', rating: 4.2, reviews: 890 },
  { id: 5, title: 'To Kill a Mockingbird', author: 'Harper Lee', price: 349, image: 'To Kill a Mockingbird.jpeg', category: 'Classic', rating: 4.6, reviews: 1560 },
  { id: 6, title: 'Pride and Prejudice', author: 'Jane Austen', price: 259, image: 'Pride and Prejudice.jpeg', category: 'Romance', rating: 4.4, reviews: 1120 },
  { id: 7, title: 'The Catcher in the Rye', author: 'J.D. Salinger', price: 289, image: 'The Catcher in the Rye.jpeg', category: 'Fiction', rating: 4.1, reviews: 750 },
  { id: 8, title: 'Lord of the Flies', author: 'William Golding', price: 269, image: 'Lord of the Flies.jpeg', category: 'Fiction', rating: 4.0, reviews: 680 },
  { id: 9, title: 'The Hobbit', author: 'J.R.R. Tolkien', price: 399, image: 'The Hobbit.jpeg', category: 'Fantasy', rating: 4.8, reviews: 2500 },
  { id: 10, title: 'Harry Potter and the Philosopher\'s Stone', author: 'J.K. Rowling', price: 359, image: 'Harry Potter and the Philosopher\'s Stone.jpeg', category: 'Fantasy', rating: 4.9, reviews: 3200 },
  { id: 11, title: 'The Da Vinci Code', author: 'Dan Brown', price: 329, image: 'The Da Vinci Code.jpeg', category: 'Thriller', rating: 4.2, reviews: 1450 },
  { id: 12, title: 'The Kite Runner', author: 'Khaled Hosseini', price: 319, image: 'The Kite Runner.jpeg', category: 'Drama', rating: 4.5, reviews: 1890 },
  { id: 13, title: 'Life of Pi', author: 'Yann Martel', price: 299, image: 'Life of Pi.jpeg', category: 'Adventure', rating: 4.3, reviews: 1100 },
  { id: 14, title: 'The Book Thief', author: 'Markus Zusak', price: 339, image: 'The Book Thief.jpeg', category: 'Historical Fiction', rating: 4.6, reviews: 1670 },
  { id: 15, title: 'Brave New World', author: 'Aldous Huxley', price: 289, image: 'Brave New World.jpeg', category: 'Science Fiction', rating: 4.1, reviews: 920 },
  { id: 16, title: 'The Chronicles of Narnia', author: 'C.S. Lewis', price: 449, image: 'The Chronicles of Narnia.jpeg', category: 'Fantasy', rating: 4.7, reviews: 2800 }
];

function getImageUrl(imageName) {
  if (!imageName) return '/default_cover.svg';
  // Try frontend public folder first, then backend uploads
  return `/${imageName}`;
}

// Star Rating Component
const StarRating = ({ rating, onRate, interactive = false }) => {
  const [hoverRating, setHoverRating] = useState(0);

  const renderStar = (index) => {
    const starValue = index + 1;
    const currentRating = interactive ? (hoverRating || rating) : rating;

    if (currentRating >= starValue) {
      return <FaStar className="text-warning" />;
    } else if (currentRating >= starValue - 0.5) {
      return <FaStarHalfAlt className="text-warning" />;
    } else {
      return <FaRegStar className="text-muted" />;
    }
  };

  return (
    <div className="d-flex align-items-center">
      {[...Array(5)].map((_, index) => (
        <span
          key={index}
          style={{ cursor: interactive ? 'pointer' : 'default' }}
          onClick={() => interactive && onRate && onRate(index + 1)}
          onMouseEnter={() => interactive && setHoverRating(index + 1)}
          onMouseLeave={() => interactive && setHoverRating(0)}
        >
          {renderStar(index)}
        </span>
      ))}
      <span className="ms-2 text-muted small">({rating.toFixed(1)})</span>
    </div>
  );
};

const Products = () => {
  const [books, setBooks] = useState([]);
  const [filteredBooks, setFilteredBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('title');
  const [userRatings, setUserRatings] = useState({});

  // Get unique categories
  const categories = ['All', ...new Set(books.map(book => book.genre || book.category))];

  // Fetch books from backend
  useEffect(() => {
    const fetchBooks = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${BACKEND_URL}/item`);
        const booksData = response.data.map(book => ({
          id: book._id,
          title: book.title,
          author: book.author,
          price: parseInt(book.price),
          image: book.itemImage,
          category: book.genre,
          rating: 4.0 + Math.random() * 1, // Random rating between 4-5
          reviews: Math.floor(Math.random() * 2000) + 500, // Random reviews 500-2500
          description: book.description
        }));
        setBooks(booksData);
        setFilteredBooks(booksData);
      } catch (error) {
        console.error('Error fetching books:', error);
        // Fallback to hardcoded books if API fails
        setBooks(ALL_BOOKS);
        setFilteredBooks(ALL_BOOKS);
      } finally {
        setLoading(false);
      }
    };

    fetchBooks();
  }, []);

  // Filter and sort books
  useEffect(() => {
    let filtered = books;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(book =>
        book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        book.author.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(book => book.category === selectedCategory);
    }

    // Sort books
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'author':
          return a.author.localeCompare(b.author);
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'rating':
          return b.rating - a.rating;
        default:
          return 0;
      }
    });

    setFilteredBooks(filtered);
  }, [books, searchTerm, selectedCategory, sortBy]);

  const handleAddToCart = (book) => {
    try {
      const cartItems = JSON.parse(localStorage.getItem('cart') || '[]');
      const existingItem = cartItems.find(item => item.id === book.id);

      if (!existingItem) {
        cartItems.push({
          ...book,
          quantity: 1
        });
        localStorage.setItem('cart', JSON.stringify(cartItems));
        alert(`"${book.title}" added to cart!`);
      } else {
        alert('Book is already in your cart!');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      alert('Failed to add item to cart');
    }
  };

  const handleRateBook = (bookId, rating) => {
    setUserRatings(prev => ({
      ...prev,
      [bookId]: rating
    }));
    // In a real app, you would send this to the backend
    alert(`You rated this book ${rating} stars!`);
  };

  return (
    <div>
      <Unavbar />

      <Container className="py-4">
        {/* Header */}
        <div className="text-center mb-4">
          <h1 className="display-4 fw-bold mb-2" style={{ color: '#FF6600', fontFamily: 'Segoe UI, sans-serif' }}>
            📚 All Books
          </h1>
          <p className="lead" style={{ color: '#666', fontFamily: 'Segoe UI, sans-serif' }}>Discover your next favorite read from our collection of {books.length} books</p>
        </div>

        {/* Search and Filter Controls */}
        <Row className="mb-4">
          <Col md={4}>
            <InputGroup>
              <InputGroup.Text>
                <FaSearch />
              </InputGroup.Text>
              <Form.Control
                type="text"
                placeholder="Search books or authors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </InputGroup>
          </Col>
          <Col md={3}>
            <Form.Select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </Form.Select>
          </Col>
          <Col md={3}>
            <Form.Select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
            >
              <option value="title">Sort by Title</option>
              <option value="author">Sort by Author</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Sort by Rating</option>
            </Form.Select>
          </Col>
          <Col md={2}>
            <Badge bg="secondary" className="p-2 w-100 text-center">
              {filteredBooks.length} Books
            </Badge>
          </Col>
        </Row>

        {/* Books Grid */}
        {loading ? (
          <div className="text-center py-5">
            <Spinner animation="border" variant="primary" style={{ width: "3rem", height: "3rem" }} />
            <h3 className="mt-3 text-muted">Loading books...</h3>
          </div>
        ) : (
          <Row className="g-4">
            {filteredBooks.length > 0 ? (
              filteredBooks.map((book) => (
                <Col key={book.id} xs={12} sm={6} md={4} lg={3}>
                  <Card className="h-100 shadow-sm book-card" style={{ transition: 'transform 0.2s' }}>
                    <div style={{ height: '280px', overflow: 'hidden' }}>
                      <Card.Img
                        variant="top"
                        src={getImageUrl(book.image)}
                        alt={book.title}
                        style={{ height: '100%', objectFit: 'cover', transition: 'transform 0.2s' }}
                        onError={(e) => {
                          e.target.src = '/default_cover.svg';
                        }}
                      />
                    </div>
                    <Card.Body className="d-flex flex-column">
                      <div className="mb-2">
                        <Badge bg="info" className="mb-2">{book.category}</Badge>
                        <Card.Title className="h6 mb-1" style={{ fontFamily: 'Segoe UI, sans-serif', color: '#333', fontWeight: '600' }}>
                          {book.title}
                        </Card.Title>
                        <Card.Text className="small mb-2" style={{ color: '#666', fontFamily: 'Segoe UI, sans-serif' }}>
                          by {book.author}
                        </Card.Text>
                      </div>

                      {/* Rating Section */}
                      <div className="mb-2">
                        <StarRating
                          rating={userRatings[book.id] || book.rating}
                          onRate={(rating) => handleRateBook(book.id, rating)}
                          interactive={true}
                        />
                        <small className="text-muted d-block">{book.reviews} reviews</small>
                      </div>

                      <div className="mt-auto">
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <span className="h5 mb-0 fw-bold" style={{ color: '#FF6600', fontFamily: 'Segoe UI, sans-serif' }}>
                            ₹{book.price}
                          </span>
                        </div>
                        <Button
                          size="sm"
                          className="w-100"
                          onClick={() => handleAddToCart(book)}
                          style={{
                            backgroundColor: '#FF6600',
                            borderColor: '#FF6600',
                            color: 'white',
                            fontFamily: 'Segoe UI, sans-serif',
                            fontWeight: '600',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '8px 16px'
                          }}
                          onMouseOver={(e) => e.target.style.backgroundColor = '#e55a00'}
                          onMouseOut={(e) => e.target.style.backgroundColor = '#FF6600'}
                        >
                          <FaShoppingCart className="me-2" />
                          Add to Cart
                        </Button>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))
            ) : (
              <Col xs={12}>
                <Alert variant="info" className="text-center">
                  <h4>No books found</h4>
                  <p>Try adjusting your search or filter criteria.</p>
                </Alert>
              </Col>
            )}
          </Row>
        )}
      </Container>

      <Footer />
    </div>
  );
};

export default Products;
