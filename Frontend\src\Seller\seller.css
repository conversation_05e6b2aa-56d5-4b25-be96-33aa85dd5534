/* Seller Interface Styles */
.seller-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.seller-navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 12px 0;
}

.seller-navbar .navbar-brand {
  font-weight: 700;
  font-size: 24px;
  color: white !important;
}

.seller-navbar .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  margin: 0 8px;
  padding: 8px 16px !important;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.seller-navbar .nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white !important;
  transform: translateY(-1px);
}

.seller-home {
  padding: 40px 20px;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 40px;
}

.dashboard-title {
  font-size: 36px;
  font-weight: 700;
  color: white;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dashboard-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 16px;
}

.stat-icon.books {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.stat-icon.orders {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

.action-btn {
  padding: 14px 28px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  color: white;
  text-decoration: none;
}

.action-btn.secondary {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
}

.action-btn.secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
  color: white;
  text-decoration: none;
}

.recent-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.item-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.item-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.item-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 12px;
}

.item-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.item-author {
  font-size: 14px;
  color: #718096;
  margin-bottom: 8px;
}

.item-price {
  font-size: 20px;
  font-weight: 700;
  color: #667eea;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #667eea;
  transition: all 0.2s ease;
}

.order-card:hover {
  background: #edf2f7;
  transform: translateX(4px);
}

.order-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 12px;
}

.order-id {
  font-weight: 600;
  color: #2d3748;
}

.order-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.order-status.pending {
  background: #fed7d7;
  color: #c53030;
}

.order-status.completed {
  background: #c6f6d5;
  color: #2f855a;
}

.order-details {
  color: #718096;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-title {
    font-size: 28px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .items-grid {
    grid-template-columns: 1fr;
  }
  
  .recent-section {
    padding: 20px;
  }
}

/* Loading States */
.loading-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  color: #718096;
}

.loading-spinner-large {
  display: inline-block;
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-radius: 50%;
  border-top-color: #667eea;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #4a5568;
}

.empty-state-description {
  font-size: 16px;
  margin-bottom: 24px;
}
