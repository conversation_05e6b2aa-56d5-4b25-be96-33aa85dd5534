import React, { useState, useEffect } from 'react';
import { FaStar, FaStarHalfAlt, FaRegStar, FaThumbsUp, FaThumbsDown } from 'react-icons/fa';
import axios from 'axios';

const BookRating = ({ bookId, currentRating = 0, totalReviews = 0, onRatingUpdate }) => {
  const [userRating, setUserRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [reviewText, setReviewText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userReview, setUserReview] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [showAllReviews, setShowAllReviews] = useState(false);

  useEffect(() => {
    fetchReviews();
    checkUserReview();
  }, [bookId]);

  const fetchReviews = async () => {
    try {
      // Mock reviews for now - replace with actual API call
      const mockReviews = [
        {
          id: 1,
          user: '<PERSON> Johnson',
          rating: 5,
          review: 'Absolutely loved this book! The characters were well-developed and the plot kept me engaged throughout.',
          date: '2024-01-15',
          helpful: 12,
          notHelpful: 1
        },
        {
          id: 2,
          user: 'Bob Smith',
          rating: 4,
          review: 'Great read with some interesting twists. Would recommend to anyone who enjoys this genre.',
          date: '2024-01-10',
          helpful: 8,
          notHelpful: 0
        },
        {
          id: 3,
          user: 'Carol Davis',
          rating: 3,
          review: 'It was okay. The story was decent but felt a bit predictable at times.',
          date: '2024-01-05',
          helpful: 3,
          notHelpful: 2
        }
      ];
      setReviews(mockReviews);
    } catch (error) {
      console.error('Error fetching reviews:', error);
    }
  };

  const checkUserReview = () => {
    // Check if user has already reviewed this book
    const user = localStorage.getItem('user');
    if (user) {
      // Mock check - replace with actual API call
      const existingReview = null; // Would come from API
      setUserReview(existingReview);
    }
  };

  const renderStars = (rating, interactive = false, size = 'text-xl') => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - Math.ceil(rating);

    // Full stars
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <button
          key={`full-${i}`}
          type="button"
          className={`${size} text-yellow-400 ${interactive ? 'hover:text-yellow-500 cursor-pointer' : ''} transition-colors`}
          onClick={interactive ? () => handleStarClick(i + 1) : undefined}
          onMouseEnter={interactive ? () => setHoverRating(i + 1) : undefined}
          onMouseLeave={interactive ? () => setHoverRating(0) : undefined}
        >
          <FaStar />
        </button>
      );
    }

    // Half star
    if (hasHalfStar && !interactive) {
      stars.push(
        <span key="half" className={`${size} text-yellow-400`}>
          <FaStarHalfAlt />
        </span>
      );
    }

    // Empty stars
    const emptyCount = interactive ? (5 - fullStars) : emptyStars;
    for (let i = 0; i < emptyCount; i++) {
      const starIndex = fullStars + (hasHalfStar && !interactive ? 1 : 0) + i;
      stars.push(
        <button
          key={`empty-${i}`}
          type="button"
          className={`${size} text-gray-300 ${interactive ? 'hover:text-yellow-500 cursor-pointer' : ''} transition-colors`}
          onClick={interactive ? () => handleStarClick(starIndex + 1) : undefined}
          onMouseEnter={interactive ? () => setHoverRating(starIndex + 1) : undefined}
          onMouseLeave={interactive ? () => setHoverRating(0) : undefined}
        >
          <FaRegStar />
        </button>
      );
    }

    return stars;
  };

  const handleStarClick = (rating) => {
    setUserRating(rating);
  };

  const handleSubmitReview = async (e) => {
    e.preventDefault();
    if (!userRating) {
      alert('Please select a rating');
      return;
    }

    setIsSubmitting(true);
    try {
      // Mock API call - replace with actual implementation
      const newReview = {
        bookId,
        rating: userRating,
        review: reviewText,
        user: JSON.parse(localStorage.getItem('user') || '{}').name || 'Anonymous'
      };

      console.log('Submitting review:', newReview);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Review submitted successfully!');
      setReviewText('');
      setUserRating(0);
      
      if (onRatingUpdate) {
        onRatingUpdate();
      }
      
      fetchReviews();
    } catch (error) {
      console.error('Error submitting review:', error);
      alert('Failed to submit review. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleHelpfulClick = (reviewId, isHelpful) => {
    // Mock implementation - replace with actual API call
    console.log(`Marking review ${reviewId} as ${isHelpful ? 'helpful' : 'not helpful'}`);
  };

  return (
    <div className="bg-amber-50 rounded-lg p-6 mt-6 border border-amber-200">
      <h4 className="font-bold text-xl text-amber-900 mb-4 font-serif-primary">
        📝 Reviews & Ratings
      </h4>

      {/* Overall Rating Display */}
      <div className="flex items-center space-x-4 mb-6 p-4 bg-white rounded-lg border border-amber-200">
        <div className="text-center">
          <div className="text-3xl font-bold text-amber-900 font-serif-primary">
            {currentRating.toFixed(1)}
          </div>
          <div className="flex justify-center mb-1">
            {renderStars(currentRating)}
          </div>
          <div className="text-sm text-amber-700">
            {totalReviews} review{totalReviews !== 1 ? 's' : ''}
          </div>
        </div>
        
        <div className="flex-1">
          <div className="space-y-1">
            {[5, 4, 3, 2, 1].map(star => (
              <div key={star} className="flex items-center space-x-2">
                <span className="text-sm w-8">{star}★</span>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-yellow-400 h-2 rounded-full" 
                    style={{ width: `${Math.random() * 100}%` }}
                  ></div>
                </div>
                <span className="text-xs text-gray-600 w-8">
                  {Math.floor(Math.random() * 50)}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* User Rating Form */}
      {!userReview && (
        <form onSubmit={handleSubmitReview} className="mb-6 p-4 bg-white rounded-lg border border-amber-200">
          <h5 className="font-semibold text-amber-900 mb-3 font-serif-secondary">
            Rate this book
          </h5>
          
          <div className="flex items-center space-x-2 mb-4">
            {renderStars(hoverRating || userRating, true, 'text-2xl')}
            <span className="ml-3 text-sm text-amber-700">
              {userRating ? `${userRating} star${userRating !== 1 ? 's' : ''}` : 'Select rating'}
            </span>
          </div>

          <textarea
            value={reviewText}
            onChange={(e) => setReviewText(e.target.value)}
            placeholder="Share your thoughts about this book... (optional)"
            className="w-full p-3 border border-amber-300 rounded-lg resize-none
                      focus:ring-2 focus:ring-amber-500 focus:border-transparent
                      font-sans text-amber-900"
            rows="3"
          />
          
          <button
            type="submit"
            disabled={isSubmitting || !userRating}
            className="mt-3 bg-gradient-to-r from-amber-600 to-orange-600 text-white 
                      px-6 py-2 rounded-lg hover:from-amber-700 hover:to-orange-700 
                      transition-all duration-300 font-semibold disabled:opacity-50
                      disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Review'}
          </button>
        </form>
      )}

      {/* Reviews List */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h5 className="font-semibold text-amber-900 font-serif-secondary">
            Customer Reviews
          </h5>
          {reviews.length > 3 && (
            <button
              onClick={() => setShowAllReviews(!showAllReviews)}
              className="text-amber-600 hover:text-amber-800 text-sm font-semibold"
            >
              {showAllReviews ? 'Show Less' : 'Show All Reviews'}
            </button>
          )}
        </div>

        {(showAllReviews ? reviews : reviews.slice(0, 3)).map((review) => (
          <div key={review.id} className="bg-white p-4 rounded-lg border border-amber-200">
            <div className="flex items-start justify-between mb-2">
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-semibold text-amber-900">{review.user}</span>
                  <div className="flex">
                    {renderStars(review.rating, false, 'text-sm')}
                  </div>
                </div>
                <div className="text-xs text-gray-600">{review.date}</div>
              </div>
            </div>
            
            <p className="text-amber-800 mb-3 font-sans leading-relaxed">
              {review.review}
            </p>
            
            <div className="flex items-center space-x-4 text-sm">
              <button
                onClick={() => handleHelpfulClick(review.id, true)}
                className="flex items-center space-x-1 text-gray-600 hover:text-green-600 transition-colors"
              >
                <FaThumbsUp />
                <span>Helpful ({review.helpful})</span>
              </button>
              <button
                onClick={() => handleHelpfulClick(review.id, false)}
                className="flex items-center space-x-1 text-gray-600 hover:text-red-600 transition-colors"
              >
                <FaThumbsDown />
                <span>({review.notHelpful})</span>
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BookRating;
