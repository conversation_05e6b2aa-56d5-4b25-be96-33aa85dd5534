const mongoose = require('mongoose');
require('./db/config');
const items = require('./db/Seller/Additem');

// Comprehensive book data with all uploaded images
const allBooks = [
  {
    itemImage: "1984 by <PERSON>.jpeg",
    title: "1984",
    author: "<PERSON>",
    genre: "Dystopian Fiction",
    description: "A dystopian social science fiction novel about totalitarian control and surveillance.",
    price: "279",
    userId: "685ed2952b3efe12dd8ace3a",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "To Kill a Mockingbird.jpeg",
    title: "To Kill a Mockingbird",
    author: "Harper Lee",
    genre: "Classic Literature",
    description: "A gripping tale of racial injustice and childhood innocence in the American South.",
    price: "349",
    userId: "685ed2952b3efe12dd8ace3b",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "The Great Gatsby.jpeg",
    title: "The Great Gatsby",
    author: "<PERSON><PERSON>",
    genre: "Classic Literature",
    description: "A classic American novel set in the Jazz Age, exploring themes of wealth, love, and the American Dream.",
    price: "299",
    userId: "685ed2952b3efe12dd8ace3c",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "The Catcher in the Rye.jpeg",
    title: "The Catcher in the Rye",
    author: "J.D. Salinger",
    genre: "Coming of Age",
    description: "A controversial novel about teenage rebellion and alienation in post-war America.",
    price: "289",
    userId: "685ed2952b3efe12dd8ace3d",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Life of Pi.jpeg",
    title: "Life of Pi",
    author: "Yann Martel",
    genre: "Adventure Fiction",
    description: "A philosophical novel about a young man's survival journey with a Bengal tiger.",
    price: "329",
    userId: "685ed2952b3efe12dd8ace3e",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Educated.jpeg",
    title: "Educated",
    author: "Tara Westover",
    genre: "Memoir",
    description: "A powerful memoir about education, family, and the struggle for self-invention.",
    price: "399",
    userId: "685ed2952b3efe12dd8ace3f",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "The Alchemist.jpeg",
    title: "The Alchemist",
    author: "Paulo Coelho",
    genre: "Philosophical Fiction",
    description: "A magical story about following your dreams and listening to your heart.",
    price: "259",
    userId: "685ed2952b3efe12dd8ace40",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "Atomic Habits.jpeg",
    title: "Atomic Habits",
    author: "James Clear",
    genre: "Self-Help",
    description: "An easy and proven way to build good habits and break bad ones.",
    price: "449",
    userId: "685ed2952b3efe12dd8ace41",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "The Kite Runner.jpeg",
    title: "The Kite Runner",
    author: "Khaled Hosseini",
    genre: "Historical Fiction",
    description: "A powerful story of friendship, betrayal, and redemption set in Afghanistan.",
    price: "369",
    userId: "685ed2952b3efe12dd8ace42",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "The Hunger Games.jpeg",
    title: "The Hunger Games",
    author: "Suzanne Collins",
    genre: "Dystopian Fiction",
    description: "A thrilling dystopian novel about survival, rebellion, and sacrifice.",
    price: "339",
    userId: "685ed2952b3efe12dd8ace43",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "The Da Vinci Code.jpeg",
    title: "The Da Vinci Code",
    author: "Dan Brown",
    genre: "Mystery Thriller",
    description: "A gripping mystery that combines art, history, and conspiracy.",
    price: "379",
    userId: "685ed2952b3efe12dd8ace44",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "The Fault in Our Stars.jpeg",
    title: "The Fault in Our Stars",
    author: "John Green",
    genre: "Young Adult Romance",
    description: "A heart-wrenching love story about two teenagers with cancer.",
    price: "309",
    userId: "685ed2952b3efe12dd8ace45",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "harry potter and the philosopher's stone.jpg",
    title: "Harry Potter and the Philosopher's Stone",
    author: "J.K. Rowling",
    genre: "Fantasy",
    description: "The magical story of a young wizard's journey at Hogwarts School of Witchcraft and Wizardry.",
    price: "399",
    userId: "685ed2952b3efe12dd8ace46",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "pride and prejudice.jpg",
    title: "Pride and Prejudice",
    author: "Jane Austen",
    genre: "Romance",
    description: "A timeless romance about love, class, and social expectations in Regency England.",
    price: "279",
    userId: "685ed2952b3efe12dd8ace47",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "the hobbit.jpeg",
    title: "The Hobbit",
    author: "J.R.R. Tolkien",
    genre: "Fantasy",
    description: "An unexpected journey of Bilbo Baggins to reclaim the lost Dwarf Kingdom of Erebor.",
    price: "329",
    userId: "685ed2952b3efe12dd8ace48",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "lord of the flies.jpeg",
    title: "Lord of the Flies",
    author: "William Golding",
    genre: "Classic Literature",
    description: "A disturbing tale of schoolboys stranded on a deserted island and their descent into savagery.",
    price: "259",
    userId: "685ed2952b3efe12dd8ace49",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "brave new world.jpeg",
    title: "Brave New World",
    author: "Aldous Huxley",
    genre: "Dystopian Fiction",
    description: "A prophetic vision of a future society where happiness is manufactured and individuality is suppressed.",
    price: "299",
    userId: "685ed2952b3efe12dd8ace4a",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "the chronicles of narnia.jpeg",
    title: "The Chronicles of Narnia",
    author: "C.S. Lewis",
    genre: "Fantasy",
    description: "A magical series about children who discover a wardrobe that leads to the land of Narnia.",
    price: "449",
    userId: "685ed2952b3efe12dd8ace4b",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "it Ends With Us.jpeg",
    title: "It Ends With Us",
    author: "Colleen Hoover",
    genre: "Contemporary Romance",
    description: "A powerful story about love, resilience, and the courage to break cycles of abuse.",
    price: "349",
    userId: "685ed2952b3efe12dd8ace4c",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "The Book Thief.jpeg",
    title: "The Book Thief",
    author: "Markus Zusak",
    genre: "Historical Fiction",
    description: "A haunting tale of a young girl living in Nazi Germany, narrated by Death himself.",
    price: "359",
    userId: "685ed2952b3efe12dd8ace4d",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "A Man Called Ove.jpeg",
    title: "A Man Called Ove",
    author: "Fredrik Backman",
    genre: "Contemporary Fiction",
    description: "A heartwarming story about a grumpy old man whose life is changed by new neighbors.",
    price: "329",
    userId: "685ed2952b3efe12dd8ace4e",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "A Thousand Boy Kisses.jpeg",
    title: "A Thousand Boy Kisses",
    author: "Tillie Cole",
    genre: "Young Adult Romance",
    description: "A beautiful and heartbreaking love story about first love and loss.",
    price: "299",
    userId: "685ed2952b3efe12dd8ace4f",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "A Thousand Splendid Suns.jpeg",
    title: "A Thousand Splendid Suns",
    author: "Khaled Hosseini",
    genre: "Historical Fiction",
    description: "A powerful story of friendship between two Afghan women spanning decades.",
    price: "379",
    userId: "685ed2952b3efe12dd8ace50",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "All the Light We Cannot See.jpeg",
    title: "All the Light We Cannot See",
    author: "Anthony Doerr",
    genre: "Historical Fiction",
    description: "A stunning novel about a blind French girl and a German boy during World War II.",
    price: "389",
    userId: "685ed2952b3efe12dd8ace51",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "Angels & Demons.jpeg",
    title: "Angels & Demons",
    author: "Dan Brown",
    genre: "Mystery Thriller",
    description: "A thrilling adventure involving the Illuminati and a deadly conspiracy at the Vatican.",
    price: "359",
    userId: "685ed2952b3efe12dd8ace52",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Behind Closed Doors.jpeg",
    title: "Behind Closed Doors",
    author: "B.A. Paris",
    genre: "Psychological Thriller",
    description: "A chilling psychological thriller about a marriage that's not what it seems.",
    price: "319",
    userId: "685ed2952b3efe12dd8ace53",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Big Little Lies.jpeg",
    title: "Big Little Lies",
    author: "Liane Moriarty",
    genre: "Contemporary Fiction",
    description: "A gripping story about three women whose lives unravel to the point of murder.",
    price: "349",
    userId: "685ed2952b3efe12dd8ace54",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "Can't Hurt Me.jpeg",
    title: "Can't Hurt Me",
    author: "David Goggins",
    genre: "Self-Help",
    description: "A powerful memoir about overcoming obstacles and achieving mental toughness.",
    price: "429",
    userId: "685ed2952b3efe12dd8ace55",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "Deep Work.jpeg",
    title: "Deep Work",
    author: "Cal Newport",
    genre: "Self-Help",
    description: "Rules for focused success in a distracted world.",
    price: "399",
    userId: "685ed2952b3efe12dd8ace56",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Emotional Intelligence.jpeg",
    title: "Emotional Intelligence",
    author: "Daniel Goleman",
    genre: "Psychology",
    description: "Why it matters more than IQ for success in life and work.",
    price: "379",
    userId: "685ed2952b3efe12dd8ace57",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Everything I Never Told You.jpeg",
    title: "Everything I Never Told You",
    author: "Celeste Ng",
    genre: "Contemporary Fiction",
    description: "A powerful story about family, identity, and the weight of expectations.",
    price: "339",
    userId: "685ed2952b3efe12dd8ace58",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Foundation.jpeg",
    title: "Foundation",
    author: "Isaac Asimov",
    genre: "Science Fiction",
    description: "The first book in Asimov's legendary Foundation series about the fall and rise of galactic civilization.",
    price: "359",
    userId: "685ed2952b3efe12dd8ace59",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "Godaan.jpeg",
    title: "Godaan",
    author: "Munshi Premchand",
    genre: "Indian Literature",
    description: "A classic Hindi novel about rural Indian life and social issues.",
    price: "249",
    userId: "685ed2952b3efe12dd8ace5a",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Gone Girl.jpeg",
    title: "Gone Girl",
    author: "Gillian Flynn",
    genre: "Psychological Thriller",
    description: "A twisted tale of a marriage gone terribly wrong.",
    price: "369",
    userId: "685ed2952b3efe12dd8ace5b",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "Great Expectations.jpeg",
    title: "Great Expectations",
    author: "Charles Dickens",
    genre: "Classic Literature",
    description: "The story of Pip's journey from humble beginnings to gentleman status.",
    price: "289",
    userId: "685ed2952b3efe12dd8ace5c",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Howl's Moving Castle.jpeg",
    title: "Howl's Moving Castle",
    author: "Diana Wynne Jones",
    genre: "Fantasy",
    description: "A magical tale about a young woman cursed to live in an old woman's body.",
    price: "319",
    userId: "685ed2952b3efe12dd8ace5d",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Hyperion.jpeg",
    title: "Hyperion",
    author: "Dan Simmons",
    genre: "Science Fiction",
    description: "A Canterbury Tales-style science fiction epic about pilgrims on a mysterious world.",
    price: "389",
    userId: "685ed2952b3efe12dd8ace5e",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "I Am Watching You.jpeg",
    title: "I Am Watching You",
    author: "Teresa Driscoll",
    genre: "Psychological Thriller",
    description: "A gripping thriller about a woman haunted by a decision she made years ago.",
    price: "329",
    userId: "685ed2952b3efe12dd8ace5f",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "I Too Had a Love Story.jpeg",
    title: "I Too Had a Love Story",
    author: "Ravinder Singh",
    genre: "Romance",
    description: "A true love story that will touch your heart and make you believe in love again.",
    price: "279",
    userId: "685ed2952b3efe12dd8ace60",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Life After Life.jpeg",
    title: "Life After Life",
    author: "Kate Atkinson",
    genre: "Historical Fiction",
    description: "A novel about living multiple lives and the choices that shape our destiny.",
    price: "359",
    userId: "685ed2952b3efe12dd8ace61",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "Little Fires Everywhere.jpeg",
    title: "Little Fires Everywhere",
    author: "Celeste Ng",
    genre: "Contemporary Fiction",
    description: "A story about family, art, identity, and the weight of secrets.",
    price: "349",
    userId: "685ed2952b3efe12dd8ace62",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "Neuromancer.jpeg",
    title: "Neuromancer",
    author: "William Gibson",
    genre: "Cyberpunk",
    description: "The groundbreaking cyberpunk novel that defined a genre.",
    price: "339",
    userId: "685ed2952b3efe12dd8ace63",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "Normal People.jpeg",
    title: "Normal People",
    author: "Sally Rooney",
    genre: "Contemporary Fiction",
    description: "A modern love story about two people who transform each other's lives.",
    price: "329",
    userId: "685ed2952b3efe12dd8ace64",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "One Hundred Years of Solitude.jpeg",
    title: "One Hundred Years of Solitude",
    author: "Gabriel García Márquez",
    genre: "Magical Realism",
    description: "A masterpiece of magical realism about the Buendía family.",
    price: "389",
    userId: "685ed2952b3efe12dd8ace65",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "Room.jpeg",
    title: "Room",
    author: "Emma Donoghue",
    genre: "Contemporary Fiction",
    description: "A powerful story told through the eyes of a five-year-old boy.",
    price: "319",
    userId: "685ed2952b3efe12dd8ace66",
    userName: "BookStore Admin",
    isTopRecommendation: false
  },
  {
    itemImage: "The Color Purple.jpeg",
    title: "The Color Purple",
    author: "Alice Walker",
    genre: "Classic Literature",
    description: "A powerful story of resilience, love, and redemption in the American South.",
    price: "339",
    userId: "685ed2952b3efe12dd8ace67",
    userName: "BookStore Admin",
    isTopRecommendation: true
  },
  {
    itemImage: "The Couple Next Door.jpeg",
    title: "The Couple Next Door",
    author: "Shari Lapena",
    genre: "Psychological Thriller",
    description: "A gripping thriller about a couple whose baby goes missing.",
    price: "309",
    userId: "685ed2952b3efe12dd8ace68",
    userName: "BookStore Admin",
    isTopRecommendation: false
  }
];

async function addAllBooks() {
  try {
    console.log('📚 Starting to add all books to database...');
    
    // Clear existing books first
    console.log('🗑️ Clearing existing books...');
    await items.deleteMany({});
    console.log('✅ Existing books cleared');
    
    // Add all books
    console.log('📖 Adding new books...');
    for (let i = 0; i < allBooks.length; i++) {
      const book = allBooks[i];
      const existingBook = await items.findOne({ title: book.title, author: book.author });
      
      if (!existingBook) {
        await items.create(book);
        console.log(`✅ Added: ${book.title} by ${book.author}`);
      } else {
        console.log(`⚠️  Book already exists: ${book.title}`);
      }
    }
    
    console.log(`\n🎉 Successfully processed ${allBooks.length} books!`);
    
    // Show final count
    const totalBooks = await items.countDocuments();
    console.log(`📊 Total books in database: ${totalBooks}`);
    
  } catch (error) {
    console.error('❌ Error adding books:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the script
addAllBooks();
