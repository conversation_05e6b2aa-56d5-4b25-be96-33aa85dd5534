const mongoose = require('mongoose');

const feedbackSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    userName: {
        type: String,
        required: true
    },
    userEmail: {
        type: String,
        required: true
    },
    bookId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Item',
        default: null // null for general website feedback
    },
    bookTitle: {
        type: String,
        default: null
    },
    rating: {
        type: Number,
        required: true,
        min: 1,
        max: 5
    },
    title: {
        type: String,
        required: true,
        maxlength: 100
    },
    comment: {
        type: String,
        required: true,
        maxlength: 1000
    },
    category: {
        type: String,
        enum: ['book', 'website', 'service', 'delivery', 'other'],
        default: 'website'
    },
    isPublic: {
        type: Boolean,
        default: true
    },
    isApproved: {
        type: Boolean,
        default: true // Auto-approve for now, can be changed later
    },
    helpfulVotes: {
        type: Number,
        default: 0
    },
    reportCount: {
        type: Number,
        default: 0
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Index for better query performance
feedbackSchema.index({ bookId: 1, rating: -1 });
feedbackSchema.index({ category: 1, createdAt: -1 });
feedbackSchema.index({ isApproved: 1, isPublic: 1 });

// Update the updatedAt field before saving
feedbackSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Virtual for getting average rating for a book
feedbackSchema.statics.getBookRating = async function(bookId) {
    const result = await this.aggregate([
        { $match: { bookId: mongoose.Types.ObjectId(bookId), isApproved: true } },
        {
            $group: {
                _id: null,
                averageRating: { $avg: '$rating' },
                totalReviews: { $sum: 1 },
                ratingDistribution: {
                    $push: '$rating'
                }
            }
        }
    ]);
    
    if (result.length > 0) {
        const distribution = [0, 0, 0, 0, 0]; // [1-star, 2-star, 3-star, 4-star, 5-star]
        result[0].ratingDistribution.forEach(rating => {
            distribution[rating - 1]++;
        });
        
        return {
            averageRating: Math.round(result[0].averageRating * 10) / 10,
            totalReviews: result[0].totalReviews,
            distribution: distribution
        };
    }
    
    return {
        averageRating: 0,
        totalReviews: 0,
        distribution: [0, 0, 0, 0, 0]
    };
};

module.exports = mongoose.model('Feedback', feedbackSchema);
