import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import axios from 'axios';

const Ssignup = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [storeName, setStoreName] = useState('');
  const [storeDesc, setStoreDesc] = useState('');

  const navigate = useNavigate();

  const handleSubmit = (e) => {
    e.preventDefault();
    let payload = { name, email, password, storeName, storeDesc };

    axios
      .post("https://booknest-backend-55yh.onrender.com/ssignup", payload)
      .then((result) => {
        alert('Seller account and bookstore created!');
        navigate('/slogin');
      })
      .catch((err) => {
        alert("Failed to create an account");
      });
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-indigo-100 to-blue-200">
      <div className="max-w-md w-full bg-white p-8 rounded-md shadow-md overflow-hidden relative">
        <div className="text-center mb-4">
          <img
            src="/bookstore-cover.jpg"
            alt="Bookstore"
            className="mx-auto mb-2 w-24 h-24 object-cover rounded-full shadow"
            onError={e => { e.target.style.display = 'none'; }}
          />
          <h2 className="text-3xl font-extrabold text-gray-900">Create Your Book Store</h2>
          <p className="text-gray-500 mt-1">Register as a seller and open your online bookstore</p>
        </div>

        <form className="space-y-5" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="storeName" className="block text-sm font-medium text-gray-700">
              Book Store Name
            </label>
            <input
              id="storeName"
              name="storeName"
              type="text"
              required
              value={storeName}
              onChange={(e) => setStoreName(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="e.g. The Reading Nook"
            />
          </div>
          <div>
            <label htmlFor="storeDesc" className="block text-sm font-medium text-gray-700">
              Store Description
            </label>
            <textarea
              id="storeDesc"
              name="storeDesc"
              rows={2}
              value={storeDesc}
              onChange={(e) => setStoreDesc(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Describe your bookstore..."
            />
          </div>
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Your Name
            </label>
            <input
              id="name"
              name="name"
              type="text"
              required
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Full Name"
            />
          </div>
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email Address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Email address"
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Password"
            />
          </div>
          <div>
            <button
              type="submit"
              className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring focus:border-indigo-300 transition-all duration-300 w-full"
            >
              Create Book Store
            </button>
          </div>
          <p className="text-sm text-gray-600 text-center">
            Already have an account?{' '}
            <Link
              to="/slogin"
              className="text-indigo-500 hover:underline focus:outline-none focus:ring focus:border-indigo-300 transition-all duration-300"
            >
              Login
            </Link>
          </p>
        </form>
        <div className="absolute h-full w-full bg-indigo-200 opacity-10 transform -skew-y-6 origin-bottom-right pointer-events-none"></div>
      </div>
    </div>
  );
};

export default Ssignup;
