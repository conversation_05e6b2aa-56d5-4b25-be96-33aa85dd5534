// Utility functions for handling images in the BookStore application

/**
 * Constructs the proper image URL for book covers
 * @param {string} itemImage - The image filename from the database
 * @returns {string} - The complete URL to the image
 */
export const getImageUrl = (itemImage) => {
  if (!itemImage) {
    return '/default_cover.svg';
  }

  // If itemImage is already a full URL, use it as is
  if (itemImage.startsWith('http')) {
    return itemImage;
  }

  // Check if it's a book cover from public folder (ends with .jpeg and is a book title)
  if (itemImage.endsWith('.jpeg') && (
    itemImage.includes('1984') ||
    itemImage.includes('Thousand') ||
    itemImage.includes('Atomic') ||
    itemImage.includes('Educated') ||
    itemImage.includes('Life of Pi') ||
    itemImage.includes('One Hundred') ||
    itemImage.includes('Alchemist') ||
    itemImage.includes('Book Thief') ||
    itemImage.includes('Catcher') ||
    itemImage.includes('Da Vinci') ||
    itemImage.includes('Fault') ||
    itemImage.includes('Girl with') ||
    itemImage.includes('Great Gatsby') ||
    itemImage.includes('Hunger Games') ||
    itemImage.includes('Kite Runner') ||
    itemImage.includes('To Kill')
  )) {
    return `/${itemImage}`;
  }

  // Otherwise, construct the URL with the backend server uploads path
  return `https://booknest-backend-55yh.onrender.com/uploads/${itemImage}`;
};

/**
 * Handles image loading errors by setting a fallback image
 * @param {Event} event - The error event from the img element
 */
export const handleImageError = (event) => {
  event.target.src = '/default_cover.svg';
  event.target.onerror = null; // Prevent infinite loop if fallback also fails
};

/**
 * Preloads an image to check if it exists
 * @param {string} imageUrl - The URL of the image to preload
 * @returns {Promise<boolean>} - Promise that resolves to true if image loads successfully
 */
export const preloadImage = (imageUrl) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = imageUrl;
  });
};

/**
 * Gets the appropriate image URL with fallback checking
 * @param {string} itemImage - The image filename from the database
 * @returns {Promise<string>} - Promise that resolves to the working image URL
 */
export const getValidImageUrl = async (itemImage) => {
  const primaryUrl = getImageUrl(itemImage);
  const isValid = await preloadImage(primaryUrl);
  
  if (isValid) {
    return primaryUrl;
  }
  
  return '/default_cover.svg';
};

export default {
  getImageUrl,
  handleImageError,
  preloadImage,
  getValidImageUrl
};
