const mongoose = require('mongoose');
require('./db/config');
const items = require('./db/Seller/Additem');

const sampleBooks = [
  {
    title: "The Great Gatsby",
    author: "<PERSON><PERSON>",
    genre: "Classic Literature",
    description: "A classic American novel set in the Jazz Age, exploring themes of wealth, love, and the American Dream.",
    price: "299",
    itemImage: "1700630708206-1979210[1].jpg",
    userId: new mongoose.Types.ObjectId(),
    userName: "Sample Seller"
  },
  {
    title: "To Kill a Mockingbird",
    author: "Harper Lee",
    genre: "Fiction",
    description: "A gripping tale of racial injustice and childhood innocence in the American South.",
    price: "349",
    itemImage: "1700631424222-18505809[1].jpg",
    userId: new mongoose.Types.ObjectId(),
    userName: "Sample Seller"
  },
  {
    title: "1984",
    author: "<PERSON>",
    genre: "Dystopian Fiction",
    description: "A dystopian social science fiction novel about totalitarian control and surveillance.",
    price: "279",
    itemImage: "1700632005607-136251[1].jpg",
    userId: new mongoose.Types.ObjectId(),
    userName: "Sample Seller"
  },
  {
    title: "Pride and Prejudice",
    author: "Jane Austen",
    genre: "Romance",
    description: "A romantic novel that critiques the British landed gentry at the end of the 18th century.",
    price: "259",
    itemImage: "1700632074778-136251.jpg",
    userId: new mongoose.Types.ObjectId(),
    userName: "Sample Seller"
  },
  {
    title: "The Catcher in the Rye",
    author: "J.D. Salinger",
    genre: "Coming of Age",
    description: "A controversial novel about teenage rebellion and alienation in post-war America.",
    price: "289",
    itemImage: "1700632515790-29502358[1].jpg",
    userId: new mongoose.Types.ObjectId(),
    userName: "Sample Seller"
  },
  {
    title: "Harry Potter and the Philosopher's Stone",
    author: "J.K. Rowling",
    genre: "Fantasy",
    description: "The first book in the magical Harry Potter series about a young wizard's adventures.",
    price: "399",
    itemImage: "1700632736939-30186948[1].jpg",
    userId: new mongoose.Types.ObjectId(),
    userName: "Sample Seller"
  },
  {
    title: "The Lord of the Rings",
    author: "J.R.R. Tolkien",
    genre: "Fantasy",
    description: "An epic high fantasy novel about the quest to destroy the One Ring.",
    price: "599",
    itemImage: "1700632928170-80830635[1].jpg",
    userId: new mongoose.Types.ObjectId(),
    userName: "Sample Seller"
  },
  {
    title: "Dune",
    author: "Frank Herbert",
    genre: "Science Fiction",
    description: "A science fiction novel set in the distant future amidst a feudal interstellar society.",
    price: "449",
    itemImage: "1700633112352-42983957[1].jpg",
    userId: new mongoose.Types.ObjectId(),
    userName: "Sample Seller"
  }
];

async function addSampleBooks() {
  try {
    console.log('Adding sample books...');
    
    // Clear existing books
    await items.deleteMany({});
    console.log('Cleared existing books');
    
    // Add sample books
    const result = await items.insertMany(sampleBooks);
    console.log(`Added ${result.length} sample books successfully!`);
    
    // Display added books
    result.forEach(book => {
      console.log(`- ${book.title} by ${book.author} ($${book.price})`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error adding sample books:', error);
    process.exit(1);
  }
}

addSampleBooks();
