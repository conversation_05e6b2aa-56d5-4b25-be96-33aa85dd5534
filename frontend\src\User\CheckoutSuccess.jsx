import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaCheckCircle, FaShoppingBag, FaHome, FaReceipt, FaSignOutAlt } from 'react-icons/fa';
import './BookStore.css';

const CheckoutSuccess = () => {
  const [user, setUser] = useState(null);
  const [orderNumber, setOrderNumber] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    // Get user from localStorage
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        setUser(JSON.parse(userData));
      } catch (e) {
        console.error('Error parsing user data:', e);
        navigate('/login');
        return;
      }
    } else {
      navigate('/login');
      return;
    }

    // Generate a random order number
    const orderNum = 'BS' + Date.now().toString().slice(-8);
    setOrderNumber(orderNum);
  }, [navigate]);

  const handleContinueShopping = () => {
    navigate('/books');
  };

  const handleViewOrders = () => {
    navigate('/myorders-new');
  };

  const handleGoHome = () => {
    navigate('/welcome');
  };

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/best-wishes');
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-teal-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-teal-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-800">Order Confirmation</h1>
            <div className="flex items-center space-x-4 text-gray-600">
              <span>Welcome, {user.name}</span>
              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 bg-red-100 text-red-700 px-3 py-2 rounded-lg text-sm font-semibold hover:bg-red-200 transition-colors"
              >
                <FaSignOutAlt />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-16">
        <div className="max-w-2xl mx-auto">
          {/* Success Icon and Message */}
          <div className="text-center mb-12">
            <div className="relative inline-block">
              <FaCheckCircle className="text-8xl text-green-500 mx-auto mb-6 animate-bounce" />
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full animate-ping"></div>
            </div>
            
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              Order Placed Successfully! 🎉
            </h2>
            
            <p className="text-xl text-gray-600 mb-6">
              Thank you for your purchase, <span className="font-semibold text-green-600">{user.name}</span>!
            </p>
            
            <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <FaReceipt className="text-2xl text-blue-600" />
                <h3 className="text-xl font-bold text-gray-800">Order Details</h3>
              </div>
              
              <div className="text-center">
                <p className="text-gray-600 mb-2">Order Number:</p>
                <p className="text-2xl font-bold text-blue-600 font-mono bg-blue-50 px-4 py-2 rounded-lg inline-block">
                  #{orderNumber}
                </p>
              </div>
              
              <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
                <p className="text-green-800 font-medium text-center">
                  📧 A confirmation email has been sent to <span className="font-bold">{user.email}</span>
                </p>
              </div>
            </div>
          </div>

          {/* Order Status Timeline */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 className="text-xl font-bold text-gray-800 mb-6 text-center">What happens next?</h3>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">✓</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-800">Order Confirmed</p>
                  <p className="text-gray-600 text-sm">Your order has been received and is being processed</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center animate-pulse">
                  <span className="text-white font-bold text-sm">2</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-800">Preparing for Shipment</p>
                  <p className="text-gray-600 text-sm">We're getting your books ready for delivery</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 font-bold text-sm">3</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-600">Shipped</p>
                  <p className="text-gray-500 text-sm">You'll receive tracking information via email</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 font-bold text-sm">4</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-600">Delivered</p>
                  <p className="text-gray-500 text-sm">Enjoy your new books!</p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="grid md:grid-cols-3 gap-4">
            <button
              onClick={handleViewOrders}
              className="flex items-center justify-center space-x-2 bg-blue-600 text-white px-6 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors transform hover:scale-105"
            >
              <FaReceipt />
              <span>View Orders</span>
            </button>
            
            <button
              onClick={handleContinueShopping}
              className="flex items-center justify-center space-x-2 bg-teal-600 text-white px-6 py-4 rounded-lg font-semibold hover:bg-teal-700 transition-colors transform hover:scale-105"
            >
              <FaShoppingBag />
              <span>Continue Shopping</span>
            </button>
            
            <button
              onClick={handleGoHome}
              className="flex items-center justify-center space-x-2 bg-gray-600 text-white px-6 py-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors transform hover:scale-105"
            >
              <FaHome />
              <span>Go Home</span>
            </button>
          </div>

          {/* Thank You Message */}
          <div className="text-center mt-12 p-6 bg-gradient-to-r from-green-100 to-teal-100 rounded-lg">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">Thank You for Choosing BookStore! 📚</h3>
            <p className="text-gray-700 leading-relaxed">
              We appreciate your business and hope you enjoy your new books. 
              If you have any questions about your order, please don't hesitate to contact our customer support team.
            </p>
            <div className="mt-4 text-4xl">
              🌟 ⭐ 📖 ⭐ 🌟
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-16">
        <div className="container mx-auto px-6 text-center">
          <p className="text-gray-300">
            © {new Date().getFullYear()} BookStore. Made with ❤️ for book lovers.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default CheckoutSuccess;
