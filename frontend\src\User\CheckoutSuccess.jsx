import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaCheckCircle, FaShoppingBag, FaHome, FaReceipt, FaSignOutAlt } from 'react-icons/fa';
import './BookStore.css';

const CheckoutSuccess = () => {
  const [user, setUser] = useState(null);
  const [orderNumber, setOrderNumber] = useState('');
  const [orderInfo, setOrderInfo] = useState(null);
  const [orderDate, setOrderDate] = useState(new Date());
  const [deliveryDate, setDeliveryDate] = useState(new Date());
  const navigate = useNavigate();

  useEffect(() => {
    // Get user from localStorage
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        setUser(JSON.parse(userData));
      } catch (e) {
        console.error('Error parsing user data:', e);
        navigate('/login');
        return;
      }
    } else {
      navigate('/login');
      return;
    }

    // Get order information from localStorage
    const lastOrderInfo = localStorage.getItem('lastOrderInfo');
    const currentOrder = localStorage.getItem('currentOrder');

    if (lastOrderInfo) {
      try {
        const orderData = JSON.parse(lastOrderInfo);
        setOrderInfo(orderData);
        setOrderDate(new Date(orderData.orderDate));

        // Use the delivery date from the order data if available, otherwise calculate
        if (orderData.deliveryDate) {
          setDeliveryDate(new Date(orderData.deliveryDate));
        } else {
          // Fallback: Calculate delivery date as 7 days from order date
          const deliveryDateTime = new Date(orderData.orderDate);
          deliveryDateTime.setDate(deliveryDateTime.getDate() + 7);
          setDeliveryDate(deliveryDateTime);
        }
      } catch (e) {
        console.error('Error parsing order info:', e);
      }
    } else if (currentOrder) {
      try {
        const orderData = JSON.parse(currentOrder);
        setOrderInfo(orderData);
        setOrderDate(new Date(orderData.orderDate));

        // Use the delivery date from the order data if available, otherwise calculate
        if (orderData.deliveryDate) {
          setDeliveryDate(new Date(orderData.deliveryDate));
        } else {
          // Fallback: Calculate delivery date as 7 days from order date
          const deliveryDateTime = new Date(orderData.orderDate);
          deliveryDateTime.setDate(deliveryDateTime.getDate() + 7);
          setDeliveryDate(deliveryDateTime);
        }
      } catch (e) {
        console.error('Error parsing current order:', e);
      }
    }

    // Generate a random order number
    const orderNum = 'BN' + Date.now().toString().slice(-8);
    setOrderNumber(orderNum);
  }, [navigate]);

  const handleContinueShopping = () => {
    navigate('/books');
  };

  const handleViewOrders = () => {
    navigate('/myorders-new');
  };

  const handleGoHome = () => {
    navigate('/welcome');
  };

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/best-wishes');
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-teal-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ background: 'linear-gradient(135deg, #fff5f5 0%, #fff0e6 50%, #f0f8ff 100%)', fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif' }}>
      {/* Header */}
      <header className="bg-white shadow-lg border-b-2" style={{ borderColor: '#FF6600' }}>
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold" style={{ color: '#FF6600' }}>Order Confirmation</h1>
            <div className="flex items-center space-x-4" style={{ color: '#333' }}>
              <span className="font-medium">Welcome, {user.name}</span>
              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-semibold transition-colors"
                style={{
                  backgroundColor: '#FF6600',
                  color: 'white',
                  border: 'none'
                }}
                onMouseOver={(e) => e.target.style.backgroundColor = '#e55a00'}
                onMouseOut={(e) => e.target.style.backgroundColor = '#FF6600'}
              >
                <FaSignOutAlt />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-16">
        <div className="max-w-2xl mx-auto">
          {/* Success Icon and Message */}
          <div className="text-center mb-12">
            <div className="relative inline-block">
              <FaCheckCircle className="text-8xl mx-auto mb-6 animate-bounce" style={{ color: '#FF6600' }} />
              <div className="absolute -top-2 -right-2 w-6 h-6 rounded-full animate-ping" style={{ backgroundColor: '#FF9900' }}></div>
            </div>

            <h2 className="text-4xl font-bold mb-4" style={{ color: '#333', fontFamily: 'Segoe UI, sans-serif' }}>
              Order Placed Successfully! 🎉
            </h2>

            <p className="text-xl mb-6" style={{ color: '#666' }}>
              Thank you for your purchase, <span className="font-semibold" style={{ color: '#FF6600' }}>{user.name}</span>!
            </p>
            
            {/* Payment Receipt */}
            <div className="bg-white rounded-lg shadow-lg p-6 mb-8 border-2 border-orange-200">
              <div className="flex items-center justify-center space-x-2 mb-6">
                <FaReceipt className="text-2xl text-orange-600" />
                <h3 className="text-xl font-bold text-gray-800">Payment Receipt</h3>
              </div>

              {/* Order Number and Date */}
              <div className="grid md:grid-cols-2 gap-4 mb-6">
                <div className="text-center">
                  <p className="text-gray-600 mb-2">Order Number:</p>
                  <p className="text-xl font-bold text-orange-600 font-mono bg-orange-50 px-4 py-2 rounded-lg">
                    #{orderNumber}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-gray-600 mb-2">Order Date:</p>
                  <p className="text-lg font-semibold text-gray-800 bg-gray-50 px-4 py-2 rounded-lg">
                    {orderDate.toLocaleDateString('en-IN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>

              {/* Customer Information */}
              <div className="border-t border-gray-200 pt-4 mb-6">
                <h4 className="font-semibold text-gray-800 mb-3">Customer Information</h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Name:</p>
                    <p className="font-medium text-gray-800">{user.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Email:</p>
                    <p className="font-medium text-gray-800">{user.email}</p>
                  </div>
                </div>
              </div>

              {/* Order Summary */}
              {orderInfo && (
                <div className="border-t border-gray-200 pt-4 mb-6">
                  <h4 className="font-semibold text-gray-800 mb-3">Order Summary</h4>
                  <div className="space-y-3">
                    {orderInfo.items && orderInfo.items.map((item, index) => (
                      <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
                        <div className="flex-1">
                          <p className="font-medium text-gray-800">{item.title}</p>
                          <p className="text-sm text-gray-600">by {item.author}</p>
                          <p className="text-sm text-gray-600">Qty: {item.quantity || 1}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-orange-600">
                            ₹{((typeof item.price === 'string' ? parseFloat(item.price.replace('₹', '')) : item.price) * (item.quantity || 1)).toFixed(2)}
                          </p>
                        </div>
                      </div>
                    ))}

                    {/* Total */}
                    <div className="border-t-2 border-orange-200 pt-3 mt-4">
                      <div className="flex justify-between items-center">
                        <p className="text-lg font-bold text-gray-800">Total Amount:</p>
                        <p className="text-xl font-bold text-orange-600">₹{orderInfo.total?.toFixed(2) || '0.00'}</p>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <p className="text-sm text-gray-600">Payment Method:</p>
                        <p className="text-sm font-medium text-gray-800">Credit/Debit Card</p>
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <p className="text-sm text-gray-600">Payment Status:</p>
                        <p className="text-sm font-medium text-green-600">✓ Paid</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Delivery Information */}
              <div className="border-t border-gray-200 pt-4 mb-4">
                <h4 className="font-semibold text-gray-800 mb-3">Delivery Information</h4>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-600">Expected Delivery:</p>
                    <p className="font-semibold text-blue-600">
                      {deliveryDate.toLocaleDateString('en-IN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    * Delivery within 7 business days from order date
                  </p>
                </div>
              </div>

              <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
                <p className="text-green-800 font-medium text-center">
                  📧 A confirmation email has been sent to <span className="font-bold">{user.email}</span>
                </p>
              </div>
            </div>
          </div>

          {/* Order Status Timeline */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 className="text-xl font-bold text-gray-800 mb-6 text-center">What happens next?</h3>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">✓</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-800">Order Confirmed</p>
                  <p className="text-gray-600 text-sm">Your order has been received and is being processed</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center animate-pulse">
                  <span className="text-white font-bold text-sm">2</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-800">Preparing for Shipment</p>
                  <p className="text-gray-600 text-sm">We're getting your books ready for delivery</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 font-bold text-sm">3</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-600">Shipped</p>
                  <p className="text-gray-500 text-sm">You'll receive tracking information via email</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 font-bold text-sm">4</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-600">Delivered</p>
                  <p className="text-gray-500 text-sm">Enjoy your new books!</p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="grid md:grid-cols-3 gap-4">
            <button
              onClick={handleViewOrders}
              className="flex items-center justify-center space-x-2 text-white px-6 py-4 rounded-lg font-semibold transition-all transform hover:scale-105 shadow-lg"
              style={{
                backgroundColor: '#FF6600',
                border: 'none',
                fontFamily: 'Segoe UI, sans-serif'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#e55a00'}
              onMouseOut={(e) => e.target.style.backgroundColor = '#FF6600'}
            >
              <FaReceipt />
              <span>View Orders</span>
            </button>

            <button
              onClick={handleContinueShopping}
              className="flex items-center justify-center space-x-2 text-white px-6 py-4 rounded-lg font-semibold transition-all transform hover:scale-105 shadow-lg"
              style={{
                backgroundColor: '#FF9900',
                border: 'none',
                fontFamily: 'Segoe UI, sans-serif'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#e68a00'}
              onMouseOut={(e) => e.target.style.backgroundColor = '#FF9900'}
            >
              <FaShoppingBag />
              <span>Continue Shopping</span>
            </button>

            <button
              onClick={handleGoHome}
              className="flex items-center justify-center space-x-2 px-6 py-4 rounded-lg font-semibold transition-all transform hover:scale-105 shadow-lg"
              style={{
                backgroundColor: 'white',
                color: '#FF6600',
                border: '2px solid #FF6600',
                fontFamily: 'Segoe UI, sans-serif'
              }}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = '#FF6600';
                e.target.style.color = 'white';
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = 'white';
                e.target.style.color = '#FF6600';
              }}
            >
              <FaHome />
              <span>Go Home</span>
            </button>
          </div>

          {/* Thank You Message */}
          <div className="text-center mt-12 p-6 rounded-lg shadow-lg" style={{ background: 'linear-gradient(135deg, #fff5f0 0%, #ffe6d9 100%)', border: '2px solid #FF6600' }}>
            <h3 className="text-2xl font-bold mb-4" style={{ color: '#FF6600', fontFamily: 'Segoe UI, sans-serif' }}>Thank You for Choosing BookNest! 📚</h3>
            <p className="leading-relaxed" style={{ color: '#333', fontFamily: 'Segoe UI, sans-serif' }}>
              We appreciate your business and hope you enjoy your new books.
              If you have any questions about your order, please don't hesitate to contact our customer support team.
            </p>
            <div className="mt-4 text-4xl">
              🌟 ⭐ 📖 ⭐ 🌟
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-16">
        <div className="container mx-auto px-6 text-center">
          <p className="text-gray-300">
            © {new Date().getFullYear()} BookStore. Made with ❤️ for book lovers.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default CheckoutSuccess;
