
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Button, Badge, Alert } from 'react-bootstrap';
import { FaBook, FaShoppingBag, FaPlus, FaEye, FaEdit, FaChartLine, FaStar } from 'react-icons/fa';
import Snavbar from './Snavbar';
import Footer from '../Components/Footer';

// BookStore Home Page (Seller Dashboard)
function Shome() {
  const [books, setBooks] = useState([]);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchSellerData();
  }, []);

  const fetchSellerData = async () => {
    try {
      setLoading(true);

      // For demo purposes, create sample seller data
      const sampleBooks = [
        {
          _id: 'book1',
          title: 'The Art of War',
          author: '<PERSON> Tzu',
          price: 299,
          category: 'Philosophy',
          itemImage: 'The Art of War by Sun Tzu.jpeg',
          description: 'Ancient Chinese military treatise',
          stock: 15,
          rating: 4.5
        },
        {
          _id: 'book2',
          title: 'Pride and Prejudice',
          author: 'Jane Austen',
          price: 349,
          category: 'Classic',
          itemImage: 'Pride and Prejudice by Jane Austen.jpeg',
          description: 'A romantic novel of manners',
          stock: 8,
          rating: 4.8
        },
        {
          _id: 'book3',
          title: 'The Catcher in the Rye',
          author: 'J.D. Salinger',
          price: 279,
          category: 'Fiction',
          itemImage: 'The Catcher in the Rye by J.D. Salinger.jpeg',
          description: 'Coming-of-age story',
          stock: 12,
          rating: 4.2
        }
      ];

      const sampleOrders = [
        {
          _id: 'order1',
          bookTitle: 'The Art of War',
          customerName: 'John Doe',
          orderDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toLocaleDateString(),
          amount: 299,
          status: 'Processing'
        },
        {
          _id: 'order2',
          bookTitle: 'Pride and Prejudice',
          customerName: 'Jane Smith',
          orderDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toLocaleDateString(),
          amount: 349,
          status: 'Delivered'
        }
      ];

      // Store in localStorage for persistence
      localStorage.setItem('sellerBooks', JSON.stringify(sampleBooks));
      localStorage.setItem('sellerOrders', JSON.stringify(sampleOrders));

      setBooks(sampleBooks);
      setOrders(sampleOrders);
    } catch (error) {
      console.error('Error fetching seller data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Calculate stats
  const totalBooks = books.length;
  const totalOrders = orders.length;
  const totalRevenue = orders.reduce((sum, order) => sum + order.amount, 0);
  const averageRating = books.length > 0 ?
    (books.reduce((sum, book) => sum + book.rating, 0) / books.length).toFixed(1) : 0;

  if (loading) {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #2c1810 0%, #8b4513 100%)' }}>
        <Snavbar />
        <Container className="py-5">
          <div className="text-center text-white">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3">Loading your dashboard...</p>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #2c1810 0%, #8b4513 100%)' }}>
      <Snavbar />

      <Container className="py-5">
        {/* Header */}
        <Row className="mb-5">
          <Col className="text-center">
            <h1 className="text-white mb-3" style={{ fontFamily: 'Playfair Display, serif', fontSize: '3rem' }}>
              Seller Dashboard
            </h1>
            <p className="text-white-50 fs-5">Manage your books and track your sales</p>
          </Col>
        </Row>

        {/* Stats Cards */}
        <Row className="mb-5">
          <Col md={3} className="mb-4">
            <Card className="h-100 shadow-lg" style={{ background: 'rgba(255,255,255,0.95)' }}>
              <Card.Body className="text-center">
                <FaBook size={40} style={{ color: '#8b4513' }} className="mb-3" />
                <h3 style={{ color: '#2c1810' }}>{totalBooks}</h3>
                <p className="text-muted mb-0">Total Books</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card className="h-100 shadow-lg" style={{ background: 'rgba(255,255,255,0.95)' }}>
              <Card.Body className="text-center">
                <FaShoppingBag size={40} style={{ color: '#8b4513' }} className="mb-3" />
                <h3 style={{ color: '#2c1810' }}>{totalOrders}</h3>
                <p className="text-muted mb-0">Total Orders</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card className="h-100 shadow-lg" style={{ background: 'rgba(255,255,255,0.95)' }}>
              <Card.Body className="text-center">
                <FaChartLine size={40} style={{ color: '#8b4513' }} className="mb-3" />
                <h3 style={{ color: '#2c1810' }}>₹{totalRevenue}</h3>
                <p className="text-muted mb-0">Total Revenue</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card className="h-100 shadow-lg" style={{ background: 'rgba(255,255,255,0.95)' }}>
              <Card.Body className="text-center">
                <FaStar size={40} style={{ color: '#8b4513' }} className="mb-3" />
                <h3 style={{ color: '#2c1810' }}>{averageRating}</h3>
                <p className="text-muted mb-0">Avg Rating</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Books Section */}
        <Card className="shadow-lg mb-5" style={{ background: 'rgba(255,255,255,0.95)' }}>
          <Card.Header style={{ background: '#8b4513', color: 'white' }}>
            <Row className="align-items-center">
              <Col>
                <h2 className="mb-0" style={{ fontFamily: 'Playfair Display, serif' }}>Your Books</h2>
              </Col>
              <Col xs="auto">
                <Button
                  variant="light"
                  onClick={() => navigate('/addbook')}
                  className="d-flex align-items-center"
                >
                  <FaPlus className="me-2" />
                  Add New Book
                </Button>
              </Col>
            </Row>
          </Card.Header>
          <Card.Body>
            {books.length === 0 ? (
              <div className="text-center py-5">
                <FaBook size={64} className="text-muted mb-3" />
                <h4 className="text-dark mb-3">No books yet</h4>
                <p className="text-muted mb-4">Start building your inventory by adding your first book!</p>
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => navigate('/addbook')}
                  style={{ background: '#8b4513', borderColor: '#8b4513' }}
                >
                  <FaPlus className="me-2" />
                  Add Your First Book
                </Button>
              </div>
            ) : (
              <Row>
                {books.map((book) => (
                  <Col key={book._id} lg={4} md={6} className="mb-4">
                    <Card className="h-100 shadow">
                      <div style={{ height: '200px', overflow: 'hidden' }}>
                        <Card.Img
                          variant="top"
                          src={book.itemImage ? `/${book.itemImage}` : '/default_cover.svg'}
                          alt={book.title}
                          style={{ height: '100%', objectFit: 'cover' }}
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = '/default_cover.svg';
                          }}
                        />
                      </div>
                      <Card.Body className="d-flex flex-column">
                        <Card.Title style={{ color: '#2c1810', fontFamily: 'Playfair Display, serif' }}>
                          {book.title}
                        </Card.Title>
                        <Card.Text className="text-muted mb-1">by {book.author}</Card.Text>
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <Badge bg="secondary">{book.category}</Badge>
                          <span className="text-muted">Stock: {book.stock}</span>
                        </div>
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <h5 style={{ color: '#8b4513' }}>₹{book.price}</h5>
                          <div className="d-flex align-items-center">
                            <FaStar style={{ color: '#ffc107' }} className="me-1" />
                            <span>{book.rating}</span>
                          </div>
                        </div>
                        <div className="d-flex gap-2 mt-auto">
                          <Button
                            variant="outline-primary"
                            size="sm"
                            className="flex-fill"
                            onClick={() => navigate(`/book/${book._id}`)}
                          >
                            <FaEye className="me-1" />
                            View
                          </Button>
                          <Button
                            variant="outline-secondary"
                            size="sm"
                            className="flex-fill"
                            onClick={() => navigate(`/editbook/${book._id}`)}
                          >
                            <FaEdit className="me-1" />
                            Edit
                          </Button>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            )}
          </Card.Body>
        </Card>

        {/* Recent Orders Section */}
        <Card className="shadow-lg" style={{ background: 'rgba(255,255,255,0.95)' }}>
          <Card.Header style={{ background: '#8b4513', color: 'white' }}>
            <h2 className="mb-0" style={{ fontFamily: 'Playfair Display, serif' }}>Recent Orders</h2>
          </Card.Header>
          <Card.Body>
            {orders.length === 0 ? (
              <div className="text-center py-4">
                <FaShoppingBag size={48} className="text-muted mb-3" />
                <h5 className="text-dark">No orders yet</h5>
                <p className="text-muted">Orders will appear here once customers start purchasing your books.</p>
              </div>
            ) : (
              <Row>
                {orders.slice(0, 3).map((order) => (
                  <Col key={order._id} md={4} className="mb-3">
                    <Card className="border">
                      <Card.Body>
                        <h6 style={{ color: '#2c1810' }}>{order.bookTitle}</h6>
                        <p className="text-muted mb-1">Customer: {order.customerName}</p>
                        <p className="text-muted mb-1">Date: {order.orderDate}</p>
                        <div className="d-flex justify-content-between align-items-center">
                          <h6 style={{ color: '#8b4513' }}>₹{order.amount}</h6>
                          <Badge bg={order.status === 'Delivered' ? 'success' : 'info'}>
                            {order.status}
                          </Badge>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            )}
          </Card.Body>
        </Card>
      </Container>
      <Footer />
    </div>
  );
}

export default Shome;
