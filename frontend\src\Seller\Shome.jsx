
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Button, Badge, Alert } from 'react-bootstrap';
import { FaBook, FaShoppingBag, FaPlus, FaEye, FaEdit, FaChartLine, FaStar } from 'react-icons/fa';
import Snavbar from './Snavbar';
import Footer from '../Components/Footer';

// BookStore Home Page (Seller Dashboard)
function Shome() {
  const [books, setBooks] = useState([]);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchSellerData();
  }, []);

  const fetchSellerData = async () => {
    try {
      setLoading(true);

      // For demo purposes, create sample seller data
      const sampleBooks = [
        {
          _id: 'book1',
          title: 'The Art of War',
          author: '<PERSON> Tzu',
          price: 299,
          category: 'Philosophy',
          itemImage: 'The Art of War by Sun Tzu.jpeg',
          description: 'Ancient Chinese military treatise',
          stock: 15,
          rating: 4.5
        },
        {
          _id: 'book2',
          title: 'Pride and Prejudice',
          author: 'Jane Austen',
          price: 349,
          category: 'Classic',
          itemImage: 'Pride and Prejudice by Jane Austen.jpeg',
          description: 'A romantic novel of manners',
          stock: 8,
          rating: 4.8
        },
        {
          _id: 'book3',
          title: 'The Catcher in the Rye',
          author: 'J.D. Salinger',
          price: 279,
          category: 'Fiction',
          itemImage: 'The Catcher in the Rye by J.D. Salinger.jpeg',
          description: 'Coming-of-age story',
          stock: 12,
          rating: 4.2
        }
      ];

      const sampleOrders = [
        {
          _id: 'order1',
          bookTitle: 'The Art of War',
          customerName: 'John Doe',
          orderDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toLocaleDateString(),
          amount: 299,
          status: 'Processing'
        },
        {
          _id: 'order2',
          bookTitle: 'Pride and Prejudice',
          customerName: 'Jane Smith',
          orderDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toLocaleDateString(),
          amount: 349,
          status: 'Delivered'
        }
      ];

      // Store in localStorage for persistence
      localStorage.setItem('sellerBooks', JSON.stringify(sampleBooks));
      localStorage.setItem('sellerOrders', JSON.stringify(sampleOrders));

      setBooks(sampleBooks);
      setOrders(sampleOrders);
    } catch (error) {
      console.error('Error fetching seller data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Calculate stats
  const totalBooks = books.length;
  const totalOrders = orders.length;
  const totalRevenue = orders.reduce((sum, order) => sum + order.amount, 0);
  const averageRating = books.length > 0 ?
    (books.reduce((sum, book) => sum + book.rating, 0) / books.length).toFixed(1) : 0;

  if (loading) {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)' }}>
        <Snavbar />
        <Container className="py-5">
          <div className="text-center">
            <div className="spinner-border text-warning" role="status" style={{ color: '#FF6600' }}>
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3" style={{ color: '#666', fontFamily: 'Segoe UI, sans-serif' }}>Loading your dashboard...</p>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)' }}>
      <Snavbar />

      <Container className="py-5">
        {/* Modern Header */}
        <Row className="mb-5">
          <Col className="text-center">
            <h1 className="mb-3" style={{
              fontFamily: 'Segoe UI, sans-serif',
              fontSize: '2.5rem',
              color: '#333',
              fontWeight: '600'
            }}>
              📊 Seller Dashboard
            </h1>
            <p className="fs-5" style={{ color: '#666', fontFamily: 'Segoe UI, sans-serif' }}>
              Manage your inventory and track performance
            </p>
          </Col>
        </Row>

        {/* Modern Stats Cards */}
        <Row className="mb-5">
          <Col md={3} className="mb-4">
            <Card className="h-100 border-0 shadow-sm" style={{
              background: 'linear-gradient(135deg, #FF6600 0%, #FF9900 100%)',
              transition: 'transform 0.2s ease-in-out'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0px)'}>
              <Card.Body className="text-center text-white p-4">
                <FaBook size={48} className="mb-3" style={{ opacity: 0.9 }} />
                <h2 className="mb-2" style={{ fontFamily: 'Segoe UI, sans-serif', fontWeight: '700' }}>{totalBooks}</h2>
                <p className="mb-0" style={{ fontFamily: 'Segoe UI, sans-serif', fontSize: '0.95rem', opacity: 0.9 }}>Total Books</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card className="h-100 border-0 shadow-sm" style={{
              background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
              transition: 'transform 0.2s ease-in-out'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0px)'}>
              <Card.Body className="text-center text-white p-4">
                <FaShoppingBag size={48} className="mb-3" style={{ opacity: 0.9 }} />
                <h2 className="mb-2" style={{ fontFamily: 'Segoe UI, sans-serif', fontWeight: '700' }}>{totalOrders}</h2>
                <p className="mb-0" style={{ fontFamily: 'Segoe UI, sans-serif', fontSize: '0.95rem', opacity: 0.9 }}>Total Orders</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card className="h-100 border-0 shadow-sm" style={{
              background: 'linear-gradient(135deg, #007bff 0%, #6610f2 100%)',
              transition: 'transform 0.2s ease-in-out'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0px)'}>
              <Card.Body className="text-center text-white p-4">
                <FaChartLine size={48} className="mb-3" style={{ opacity: 0.9 }} />
                <h2 className="mb-2" style={{ fontFamily: 'Segoe UI, sans-serif', fontWeight: '700' }}>₹{totalRevenue}</h2>
                <p className="mb-0" style={{ fontFamily: 'Segoe UI, sans-serif', fontSize: '0.95rem', opacity: 0.9 }}>Total Revenue</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3} className="mb-4">
            <Card className="h-100 border-0 shadow-sm" style={{
              background: 'linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)',
              transition: 'transform 0.2s ease-in-out'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0px)'}>
              <Card.Body className="text-center text-white p-4">
                <FaStar size={48} className="mb-3" style={{ opacity: 0.9 }} />
                <h2 className="mb-2" style={{ fontFamily: 'Segoe UI, sans-serif', fontWeight: '700' }}>{averageRating}</h2>
                <p className="mb-0" style={{ fontFamily: 'Segoe UI, sans-serif', fontSize: '0.95rem', opacity: 0.9 }}>Avg Rating</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Modern Books Section */}
        <Card className="border-0 shadow-sm mb-5" style={{ background: '#ffffff' }}>
          <Card.Header style={{
            background: 'linear-gradient(135deg, #FF6600 0%, #FF9900 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px 8px 0 0'
          }}>
            <Row className="align-items-center">
              <Col>
                <h3 className="mb-0" style={{
                  fontFamily: 'Segoe UI, sans-serif',
                  fontWeight: '600',
                  fontSize: '1.5rem'
                }}>
                  📚 Your Book Inventory
                </h3>
              </Col>
              <Col xs="auto">
                <Button
                  style={{
                    background: 'rgba(255,255,255,0.2)',
                    border: '2px solid rgba(255,255,255,0.3)',
                    color: 'white',
                    fontFamily: 'Segoe UI, sans-serif',
                    fontWeight: '600',
                    borderRadius: '8px',
                    transition: 'all 0.2s ease-in-out'
                  }}
                  onClick={() => navigate('/addbook')}
                  className="d-flex align-items-center"
                  onMouseEnter={(e) => {
                    e.target.style.background = 'rgba(255,255,255,0.3)';
                    e.target.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = 'rgba(255,255,255,0.2)';
                    e.target.style.transform = 'translateY(0px)';
                  }}
                >
                  <FaPlus className="me-2" />
                  Add New Book
                </Button>
              </Col>
            </Row>
          </Card.Header>
          <Card.Body style={{ padding: '2rem' }}>
            {books.length === 0 ? (
              <div className="text-center py-5">
                <div style={{
                  background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                  borderRadius: '50%',
                  width: '120px',
                  height: '120px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 2rem auto'
                }}>
                  <FaBook size={48} style={{ color: '#FF6600' }} />
                </div>
                <h4 style={{
                  color: '#333',
                  fontFamily: 'Segoe UI, sans-serif',
                  fontWeight: '600',
                  marginBottom: '1rem'
                }}>
                  No books in your inventory yet
                </h4>
                <p style={{
                  color: '#666',
                  fontFamily: 'Segoe UI, sans-serif',
                  fontSize: '1.1rem',
                  marginBottom: '2rem'
                }}>
                  Start building your bookstore by adding your first book!
                </p>
                <Button
                  size="lg"
                  onClick={() => navigate('/addbook')}
                  style={{
                    background: 'linear-gradient(135deg, #FF6600 0%, #FF9900 100%)',
                    border: 'none',
                    borderRadius: '8px',
                    fontFamily: 'Segoe UI, sans-serif',
                    fontWeight: '600',
                    padding: '12px 30px',
                    fontSize: '1.1rem',
                    transition: 'all 0.2s ease-in-out'
                  }}
                  onMouseEnter={(e) => e.target.style.transform = 'translateY(-2px)'}
                  onMouseLeave={(e) => e.target.style.transform = 'translateY(0px)'}
                >
                  <FaPlus className="me-2" />
                  Add Your First Book
                </Button>
              </div>
            ) : (
              <Row>
                {books.map((book) => (
                  <Col key={book._id} lg={4} md={6} className="mb-4">
                    <Card className="h-100 border-0 shadow-sm" style={{
                      transition: 'all 0.2s ease-in-out',
                      borderRadius: '12px'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-5px)';
                      e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0px)';
                      e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                    }}>
                      <div style={{ height: '220px', overflow: 'hidden', borderRadius: '12px 12px 0 0' }}>
                        <Card.Img
                          variant="top"
                          src={book.itemImage ? `/${book.itemImage}` : '/default_cover.svg'}
                          alt={book.title}
                          style={{
                            height: '100%',
                            objectFit: 'cover',
                            transition: 'transform 0.2s ease-in-out'
                          }}
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = '/default_cover.svg';
                          }}
                          onMouseEnter={(e) => e.target.style.transform = 'scale(1.05)'}
                          onMouseLeave={(e) => e.target.style.transform = 'scale(1)'}
                        />
                      </div>
                      <Card.Body className="d-flex flex-column" style={{ padding: '1.5rem' }}>
                        <Card.Title style={{
                          color: '#333',
                          fontSize: '1.2rem',
                          fontFamily: 'Segoe UI, sans-serif',
                          fontWeight: '600',
                          marginBottom: '0.5rem'
                        }}>
                          {book.title}
                        </Card.Title>
                        <Card.Text style={{
                          color: '#666',
                          marginBottom: '0.5rem',
                          fontFamily: 'Segoe UI, sans-serif'
                        }}>
                          by {book.author}
                        </Card.Text>
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <Badge style={{
                            background: 'linear-gradient(135deg, #007bff 0%, #6610f2 100%)',
                            border: 'none',
                            borderRadius: '15px',
                            padding: '4px 10px',
                            fontFamily: 'Segoe UI, sans-serif',
                            fontSize: '0.75rem'
                          }}>
                            {book.genre || book.category || 'General'}
                          </Badge>
                          {book.isTopRecommendation && (
                            <Badge style={{
                              background: 'linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)',
                              border: 'none',
                              borderRadius: '15px',
                              padding: '4px 10px',
                              fontFamily: 'Segoe UI, sans-serif',
                              fontSize: '0.75rem'
                            }}>
                              <FaStar className="me-1" />
                              Top Pick
                            </Badge>
                          )}
                        </div>
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <h4 className="mb-0" style={{
                            color: '#FF6600',
                            fontFamily: 'Segoe UI, sans-serif',
                            fontWeight: '700'
                          }}>
                            ₹{book.price}
                          </h4>
                          <div className="d-flex align-items-center">
                            <FaStar style={{ color: '#ffc107' }} className="me-1" />
                            <span style={{
                              fontFamily: 'Segoe UI, sans-serif',
                              fontWeight: '600',
                              color: '#333'
                            }}>
                              {book.rating || '4.5'}
                            </span>
                          </div>
                        </div>
                        <div className="d-flex gap-2 mt-auto">
                          <Button
                            size="sm"
                            className="flex-fill"
                            style={{
                              background: 'transparent',
                              border: '2px solid #FF6600',
                              color: '#FF6600',
                              borderRadius: '8px',
                              fontFamily: 'Segoe UI, sans-serif',
                              fontWeight: '600',
                              transition: 'all 0.2s ease-in-out'
                            }}
                            onClick={() => navigate(`/book/${book._id}`)}
                            onMouseEnter={(e) => {
                              e.target.style.background = '#FF6600';
                              e.target.style.color = 'white';
                            }}
                            onMouseLeave={(e) => {
                              e.target.style.background = 'transparent';
                              e.target.style.color = '#FF6600';
                            }}
                          >
                            <FaEye className="me-1" />
                            View
                          </Button>
                          <Button
                            size="sm"
                            className="flex-fill"
                            style={{
                              background: 'transparent',
                              border: '2px solid #28a745',
                              color: '#28a745',
                              borderRadius: '8px',
                              fontFamily: 'Segoe UI, sans-serif',
                              fontWeight: '600',
                              transition: 'all 0.2s ease-in-out'
                            }}
                            onClick={() => navigate(`/editbook/${book._id}`)}
                            onMouseEnter={(e) => {
                              e.target.style.background = '#28a745';
                              e.target.style.color = 'white';
                            }}
                            onMouseLeave={(e) => {
                              e.target.style.background = 'transparent';
                              e.target.style.color = '#28a745';
                            }}
                          >
                            <FaEdit className="me-1" />
                            Edit
                          </Button>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            )}
          </Card.Body>
        </Card>

        {/* Modern Recent Orders Section */}
        <Card className="border-0 shadow-sm" style={{ background: '#ffffff' }}>
          <Card.Header style={{
            background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px 8px 0 0'
          }}>
            <h3 className="mb-0" style={{
              fontFamily: 'Segoe UI, sans-serif',
              fontWeight: '600',
              fontSize: '1.5rem'
            }}>
              🛒 Recent Orders
            </h3>
          </Card.Header>
          <Card.Body style={{ padding: '2rem' }}>
            {orders.length === 0 ? (
              <div className="text-center py-5">
                <div style={{
                  background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                  borderRadius: '50%',
                  width: '120px',
                  height: '120px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 2rem auto'
                }}>
                  <FaShoppingBag size={48} style={{ color: '#28a745' }} />
                </div>
                <h4 style={{
                  color: '#333',
                  fontFamily: 'Segoe UI, sans-serif',
                  fontWeight: '600',
                  marginBottom: '1rem'
                }}>
                  No orders yet
                </h4>
                <p style={{
                  color: '#666',
                  fontFamily: 'Segoe UI, sans-serif',
                  fontSize: '1.1rem'
                }}>
                  Orders will appear here once customers start purchasing your books.
                </p>
              </div>
            ) : (
              <Row>
                {orders.slice(0, 3).map((order) => (
                  <Col key={order._id} md={4} className="mb-3">
                    <Card className="border">
                      <Card.Body>
                        <h6 style={{ color: '#2c1810' }}>{order.bookTitle}</h6>
                        <p className="text-muted mb-1">Customer: {order.customerName}</p>
                        <p className="text-muted mb-1">Date: {order.orderDate}</p>
                        <div className="d-flex justify-content-between align-items-center">
                          <h6 style={{ color: '#8b4513' }}>₹{order.amount}</h6>
                          <Badge bg={order.status === 'Delivered' ? 'success' : 'info'}>
                            {order.status}
                          </Badge>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            )}
          </Card.Body>
        </Card>
      </Container>
      <Footer />
    </div>
  );
}

export default Shome;
