
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Card, Button, Form, Modal } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';

// Simple Navbar for Book Store
function BookStoreNavbar() {
  return (
    <nav style={{ background: '#2d3748', padding: '1rem', color: '#fff', marginBottom: '2rem' }}>
      <h2 style={{ margin: 0, fontWeight: 700 }}>Book Store</h2>
    </nav>
  );
}

// Book List Component
function BookList({ books, onAddToCart }) {
  return (
    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2rem', justifyContent: 'center' }}>
      {books.map((book) => (
        <Card key={book._id} style={{ width: '250px', boxShadow: '0 2px 8px #ccc', borderRadius: '10px' }}>
          <Card.Img
            variant="top"
            src={book.coverImage ? process.env.PUBLIC_URL + '/covers/' + book.coverImage : process.env.PUBLIC_URL + '/covers/default.jpg'}
            alt={book.title}
            style={{ height: '300px', objectFit: 'cover', borderTopLeftRadius: '10px', borderTopRightRadius: '10px' }}
          />
          <Card.Body>
            <Card.Title>{book.title}</Card.Title>
            <Card.Text>
              <b>Author:</b> {book.author}<br />
              <b>Price:</b> ₹{book.price}
            </Card.Text>
            <Button variant="primary" onClick={() => onAddToCart(book)}>
              Add to Cart
            </Button>
          </Card.Body>
        </Card>
      ))}
    </div>
  );
}

// Cart Component
function Cart({ cart, onCheckout, onRemove }) {
  const total = cart.reduce((sum, item) => sum + item.price * item.qty, 0);
  return (
    <div style={{ margin: '2rem auto', maxWidth: '600px', background: '#f7fafc', padding: '1.5rem', borderRadius: '10px' }}>
      <h4>Your Cart</h4>
      {cart.length === 0 ? (
        <p>No books in cart.</p>
      ) : (
        <>
          <ul style={{ listStyle: 'none', padding: 0 }}>
            {cart.map((item) => (
              <li key={item._id} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                <span>
                  {item.title} (x{item.qty}) - ₹{item.price * item.qty}
                </span>
                <Button variant="danger" size="sm" onClick={() => onRemove(item._id)}>
                  Remove
                </Button>
              </li>
            ))}
          </ul>
          <div style={{ fontWeight: 600, marginBottom: '1rem' }}>Total: ₹{total}</div>
          <Button variant="success" onClick={onCheckout}>Checkout</Button>
        </>
      )}
    </div>
  );
}

// Checkout Modal
function CheckoutModal({ show, onHide, onConfirm }) {
  const [form, setForm] = useState({
    name: '',
    address: '',
    email: '',
  });

  const handleChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  const handleSubmit = (e) => {
    e.preventDefault();
    onConfirm(form);
  };

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Checkout</Modal.Title>
      </Modal.Header>
      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          <Form.Group>
            <Form.Label>Name</Form.Label>
            <Form.Control name="name" value={form.name} onChange={handleChange} required />
          </Form.Group>
          <Form.Group>
            <Form.Label>Address</Form.Label>
            <Form.Control name="address" value={form.address} onChange={handleChange} required />
          </Form.Group>
          <Form.Group>
            <Form.Label>Email</Form.Label>
            <Form.Control name="email" type="email" value={form.email} onChange={handleChange} required />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide}>Cancel</Button>
          <Button variant="primary" type="submit">Confirm Order</Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
}

// Orders Page (for user to see their orders)
function OrdersPage({ orders }) {
  return (
    <div style={{ maxWidth: '900px', margin: '2rem auto' }}>
      <h3 className="text-3xl font-semibold mb-4 text-center">Your Orders</h3>
      {orders.length === 0 ? (
        <p>No orders yet.</p>
      ) : (
        orders.map((order) => (
          <Card key={order._id} style={{ marginBottom: '1.5rem', boxShadow: '0 2px 8px #ccc', borderRadius: '10px' }}>
            <Card.Body>
              <Card.Title>Order #{order._id.slice(0, 8)}</Card.Title>
              <Card.Text>
                <b>Name:</b> {order.name}<br />
                <b>Address:</b> {order.address}<br />
                <b>Email:</b> {order.email}<br />
                <b>Date:</b> {new Date(order.date).toLocaleString()}<br />
                <b>Status:</b> {order.status}
              </Card.Text>
              <div>
                <b>Books:</b>
                <ul>
                  {order.books.map((b, idx) => (
                    <li key={idx}>{b.title} (x{b.qty}) - ₹{b.price * b.qty}</li>
                  ))}
                </ul>
                <b>Total:</b> ₹{order.total}
              </div>
            </Card.Body>
          </Card>
        ))
      )}
    </div>
  );
}

// Main BookStore App
function BookStore() {
  const [books, setBooks] = useState([]);
  const [cart, setCart] = useState([]);
  const [showCheckout, setShowCheckout] = useState(false);
  const [orders, setOrders] = useState([]);
  const [showOrders, setShowOrders] = useState(false);

  // Fetch books from backend
  useEffect(() => {
    // Fetch books from the correct backend API
    axios.get('https://booknest-backend-55yh.onrender.com/books')
      .then(res => setBooks(res.data))
      .catch(() => {
        // fallback demo books
        setBooks([
          {
            _id: '1',
            title: 'The Great Gatsby',
            author: 'F. Scott Fitzgerald',
            price: 299,
            coverImage: 'gatsby.jpg'
          },
          {
            _id: '2',
            title: 'To Kill a Mockingbird',
            author: 'Harper Lee',
            price: 349,
            coverImage: 'mockingbird.jpg'
          },
          {
            _id: '3',
            title: '1984',
            author: 'George Orwell',
            price: 199,
            coverImage: '1984.jpg'
          },
          {
            _id: '4',
            title: 'Pride and Prejudice',
            author: 'Jane Austen',
            price: 249,
            coverImage: 'pride.jpg'
          }
        ]);
      });
  }, []);

  // Load orders from localStorage (simulate user orders)
  useEffect(() => {
    const savedOrders = JSON.parse(localStorage.getItem('orders') || '[]');
    setOrders(savedOrders);
  }, []);

  // Add book to cart
  const handleAddToCart = (book) => {
    setCart((prev) => {
      const found = prev.find((b) => b._id === book._id);
      if (found) {
        return prev.map((b) => b._id === book._id ? { ...b, qty: b.qty + 1 } : b);
      }
      return [...prev, { ...book, qty: 1 }];
    });
  };

  // Remove book from cart
  const handleRemoveFromCart = (id) => {
    setCart((prev) => prev.filter((b) => b._id !== id));
  };

  // Handle checkout
  const handleCheckout = () => {
    setShowCheckout(true);
  };

  // Confirm order
  const handleConfirmOrder = (form) => {
    const order = {
      _id: Math.random().toString(36).substr(2, 9),
      name: form.name,
      address: form.address,
      email: form.email,
      date: new Date().toISOString(),
      books: cart,
      total: cart.reduce((sum, item) => sum + item.price * item.qty, 0),
      status: 'Processing'
    };
    const updatedOrders = [order, ...orders];
    setOrders(updatedOrders);
    localStorage.setItem('orders', JSON.stringify(updatedOrders));
    setCart([]);
    setShowCheckout(false);
    setShowOrders(true);
  };

  return (
    <div>
      <BookStoreNavbar />
      <div style={{ display: 'flex', justifyContent: 'center', gap: '2rem', marginBottom: '2rem' }}>
        <Button variant="info" onClick={() => setShowOrders(false)}>Browse Books</Button>
        <Button variant="secondary" onClick={() => setShowOrders(true)}>My Orders</Button>
      </div>
      {!showOrders && (
        <>
          <BookList books={books} onAddToCart={handleAddToCart} />
          <Cart cart={cart} onCheckout={handleCheckout} onRemove={handleRemoveFromCart} />
          <CheckoutModal
            show={showCheckout}
            onHide={() => setShowCheckout(false)}
            onConfirm={handleConfirmOrder}
          />
        </>
      )}
      {showOrders && <OrdersPage orders={orders} />}
      <footer style={{ textAlign: 'center', marginTop: '3rem', padding: '2rem 0', background: '#f1f1f1' }}>
        <span>© {new Date().getFullYear()} Book Store. All rights reserved.</span>
      </footer>
    </div>
  );
}

export default BookStore;
