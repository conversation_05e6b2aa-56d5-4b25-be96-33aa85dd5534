/* Book Store App-wide styles */
body {
  font-family: 'Segoe UI', Arial, sans-serif;
  background-color: #f0fdfa;
  margin: 0;
  padding: 0;
}

/* Book List styles */
.book-list {
  max-width: 1400px;
  margin: 0 auto;
  padding: 32px 16px;
}

/* Book Grid styles */
.book-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  justify-content: center;
}

/* Book Card styles */
.book-card {
  background-color: #fff;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  border-radius: 10px;
  padding: 24px 18px 18px 18px;
  text-align: center;
  width: 260px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.2s, transform 0.2s;
  position: relative;
}

.book-card:hover {
  box-shadow: 0 8px 24px rgba(0,0,0,0.16);
  transform: translateY(-4px) scale(1.04);
}

.book-card img {
  max-width: 140px;
  max-height: 210px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 18px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
}

.book-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #22223b;
}

.book-author {
  font-size: 0.98rem;
  color: #4a4e69;
  margin-bottom: 10px;
}

.book-price {
  font-size: 1.05rem;
  color: #9a031e;
  font-weight: 700;
  margin-bottom: 12px;
}

.book-description {
  font-size: 0.95rem;
  color: #444;
  margin-bottom: 14px;
  min-height: 48px;
}

.add-to-cart-btn {
  background-color: #3a86ff;
  color: #fff;
  border: none;
  border-radius: 5px;
  padding: 9px 18px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.18s;
  margin-top: auto;
}

.add-to-cart-btn:hover {
  background-color: #26408b;
}

/* Search and Filter styles */
.search-filter-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 28px;
  justify-content: center;
}

.input-container {
  position: relative;
  width: 260px;
}

input[type="text"], select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #bfc0c0;
  border-radius: 4px;
  font-size: 1rem;
  background: #fff;
  transition: border 0.2s;
}

input[type="text"]:focus, select:focus {
  border-color: #3a86ff;
  outline: none;
}

label {
  position: absolute;
  top: 50%;
  left: 14px;
  transform: translateY(-50%);
  background: #fff;
  padding: 0 4px;
  color: #888;
  font-size: 0.98rem;
  pointer-events: none;
  transition: 0.2s;
}

input[type="text"]:focus + label,
input[type="text"]:not(:placeholder-shown) + label {
  top: -10px;
  left: 8px;
  font-size: 0.85rem;
  color: #3a86ff;
  background: #fff;
  padding: 0 4px;
}

/* Responsive Design */
@media (max-width: 900px) {
  .book-grid {
    gap: 20px;
  }
  .book-card {
    width: 48vw;
    min-width: 220px;
    max-width: 320px;
  }
}

@media (max-width: 600px) {
  .book-list {
    padding: 12px 2vw;
  }
  .book-grid {
    flex-direction: column;
    gap: 18px;
  }
  .book-card {
    width: 100%;
    min-width: 0;
    max-width: 100%;
  }
  .search-filter-bar {
    flex-direction: column;
    gap: 10px;
  }
}

/* Cart styles */
.cart-container {
  position: fixed;
  top: 0;
  right: 0;
  width: 340px;
  max-width: 90vw;
  height: 100vh;
  background: #fff;
  box-shadow: -2px 0 16px rgba(0,0,0,0.13);
  z-index: 1000;
  padding: 28px 18px 18px 18px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.cart-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 18px;
  color: #22223b;
}

.cart-item {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.cart-item img {
  width: 48px;
  height: 68px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 12px;
}

.cart-item-details {
  flex: 1;
}

.cart-item-title {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 2px;
}

.cart-item-author {
  font-size: 0.92rem;
  color: #4a4e69;
}

.cart-item-price {
  font-size: 0.98rem;
  color: #9a031e;
  font-weight: 600;
}

.remove-cart-btn {
  background: none;
  border: none;
  color: #e63946;
  font-size: 1.1rem;
  cursor: pointer;
  margin-left: 8px;
  transition: color 0.18s;
}

.remove-cart-btn:hover {
  color: #b5171e;
}

.cart-total {
  font-size: 1.08rem;
  font-weight: 700;
  margin-top: 18px;
  color: #22223b;
}

.checkout-btn {
  background-color: #43aa8b;
  color: #fff;
  border: none;
  border-radius: 5px;
  padding: 11px 0;
  font-size: 1.05rem;
  cursor: pointer;
  margin-top: 18px;
  transition: background 0.18s;
  width: 100%;
}

.checkout-btn:hover {
  background-color: #277c6a;
}

/* Utility classes */
.hide {
  display: none !important;
}