import React, { useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import Snavbar from './Snavbar';

// AddBook component for adding a new book to the store
function AddBook() {
  const [formData, setFormData] = useState({
    title: '',
    author: '',
    genre: '',
    price: '',
    description: '',
    coverImage: null, // for file upload
  });

  const navigate = useNavigate();
  const user = JSON.parse(localStorage.getItem('user'));

  // Handle input changes
  const handleChange = (e) => {
    const { name, value, files } = e.target;
    if (name === 'coverImage') {
      setFormData({ ...formData, coverImage: files[0] });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const bookData = new FormData();
      bookData.append('title', formData.title);
      bookData.append('author', formData.author);
      bookData.append('genre', formData.genre);
      bookData.append('price', formData.price);
      bookData.append('description', formData.description);
      if (formData.coverImage) {
        bookData.append('coverImage', formData.coverImage);
      }
      if (user) {
        bookData.append('userName', user.name);
        bookData.append('userId', user.id);
      }

      await axios.post('https://booknest-backend-55yh.onrender.com/books', bookData);
      alert('Book added successfully!');
      navigate('/mybooks');
    } catch (error) {
      console.error('Error adding book:', error);
      alert('Failed to add book. Please try again.');
    }
  };

  return (
    <div>
      <Snavbar />
      <div className="max-w-md mx-auto mt-8 p-4 border rounded shadow-lg" style={{ backgroundColor: "lightskyblue" }}>
        <h2 className="text-2xl font-semibold mb-4">Add Book</h2>
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <input
              type="text"
              name="title"
              placeholder="Book Title"
              value={formData.title}
              onChange={handleChange}
              className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              required
            />
          </div>
          <div className="mb-4">
            <input
              type="text"
              name="author"
              placeholder="Author"
              value={formData.author}
              onChange={handleChange}
              className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              required
            />
          </div>
          <div className="mb-4">
            <input
              type="text"
              name="genre"
              placeholder="Genre"
              value={formData.genre}
              onChange={handleChange}
              className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              required
            />
          </div>
          <div className="mb-4">
            <input
              type="number"
              name="price"
              placeholder="Price"
              value={formData.price}
              onChange={handleChange}
              className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              min="0"
              step="0.01"
              required
            />
          </div>
          <div className="mb-4">
            <textarea
              name="description"
              placeholder="Description"
              value={formData.description}
              onChange={handleChange}
              className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              required
              rows={3}
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-600 mb-1">Book Cover Image</label>
            <input
              type="file"
              name="coverImage"
              accept="image/*"
              onChange={handleChange}
              className="border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              required
            />
            <small className="text-gray-500">Upload a cover image from the public folder if available.</small>
          </div>
          <button
            type="submit"
            className="bg-blue-900 hover:bg-blue-500 text-white font-semibold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Add Book
          </button>
        </form>
      </div>
    </div>
  );
}

export default AddBook;
