import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaBook, FaHeart, FaStar, FaArrowRight, FaHome, FaSignInAlt } from 'react-icons/fa';
import './BookStore.css';

const BestWishes = () => {
  const [showAnimation, setShowAnimation] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Trigger animation after component mounts
    setTimeout(() => setShowAnimation(true), 500);
  }, []);

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleLogin = () => {
    navigate('/login');
  };

  const handleBrowseBooks = () => {
    navigate('/books');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50 overflow-hidden">


      {/* Main Content */}
      <main className="container mx-auto px-6 py-16 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main Message */}
          <div className={`transition-all duration-1000 ${showAnimation ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-10'}`}>
            <div className="mb-8">
              <div className="relative inline-block">
                <FaHeart className="text-8xl text-pink-500 mx-auto mb-6 animate-pulse" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-pink-400 rounded-full animate-ping"></div>
              </div>
            </div>

            <h1 className="text-6xl font-bold text-gray-800 mb-6 leading-tight">
              Thank You & 
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600"> Best Wishes!</span>
            </h1>

            <p className="text-2xl text-gray-600 mb-8 leading-relaxed max-w-3xl mx-auto">
              It was wonderful having you at BookStore! We hope you found exactly what you were looking for.
              Until we meet again, happy reading!
            </p>
          </div>

          {/* Inspirational Quote */}
          <div className={`transition-all duration-1000 delay-300 ${showAnimation ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-10'}`}>
            <div className="bg-white rounded-2xl shadow-2xl p-8 mb-12 border-l-4 border-purple-500">
              <blockquote className="text-xl italic text-gray-700 mb-4">
                "A reader lives a thousand lives before he dies... The man who never reads lives only one."
              </blockquote>
              <cite className="text-purple-600 font-semibold">— George R.R. Martin</cite>
            </div>
          </div>

          {/* Feature Cards */}
          <div className={`transition-all duration-1000 delay-500 ${showAnimation ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-10'}`}>
            <div className="grid md:grid-cols-3 gap-8 mb-12">
              {/* Come Back Soon */}
              <div className="bg-white rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="text-center">
                  <div className="bg-gradient-to-r from-pink-100 to-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FaBook className="text-2xl text-purple-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-3">Come Back Soon</h3>
                  <p className="text-gray-600 text-sm">
                    We're always adding new books and features. Your next great read is waiting!
                  </p>
                </div>
              </div>

              {/* Stay Connected */}
              <div className="bg-white rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="text-center">
                  <div className="bg-gradient-to-r from-blue-100 to-teal-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FaHeart className="text-2xl text-teal-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-3">Stay Connected</h3>
                  <p className="text-gray-600 text-sm">
                    Follow us for book recommendations, author interviews, and reading tips.
                  </p>
                </div>
              </div>

              {/* Share the Love */}
              <div className="bg-white rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="text-center">
                  <div className="bg-gradient-to-r from-orange-100 to-pink-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FaStar className="text-2xl text-orange-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-3">Share the Love</h3>
                  <p className="text-gray-600 text-sm">
                    Tell your friends about BookStore and spread the joy of reading together.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className={`transition-all duration-1000 delay-700 ${showAnimation ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-10'}`}>
            <div className="grid md:grid-cols-3 gap-4 mb-12">
              <button
                onClick={handleLogin}
                className="flex items-center justify-center space-x-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 shadow-lg"
              >
                <FaSignInAlt />
                <span>Sign In Again</span>
              </button>
              
              <button
                onClick={handleBrowseBooks}
                className="flex items-center justify-center space-x-2 bg-gradient-to-r from-teal-600 to-blue-600 text-white px-8 py-4 rounded-xl font-bold hover:from-teal-700 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg"
              >
                <FaBook />
                <span>Browse Books</span>
              </button>
              
              <button
                onClick={handleBackToHome}
                className="flex items-center justify-center space-x-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white px-8 py-4 rounded-xl font-bold hover:from-gray-700 hover:to-gray-800 transition-all transform hover:scale-105 shadow-lg"
              >
                <FaHome />
                <span>Home</span>
              </button>
            </div>
          </div>

          {/* Final Message */}
          <div className={`transition-all duration-1000 delay-900 ${showAnimation ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-10'}`}>
            <div className="bg-gradient-to-r from-purple-100 via-pink-100 to-orange-100 rounded-2xl p-8 shadow-lg">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">
                Keep Reading, Keep Growing! 🌱
              </h2>
              <p className="text-lg text-gray-700 leading-relaxed mb-6">
                Every book is a new adventure, every page a new discovery. 
                Thank you for being part of our BookStore family. We can't wait to see you again!
              </p>

            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-16 relative z-10">
        <div className="container mx-auto px-6 text-center">
          <p className="text-gray-300 mb-2">
            © {new Date().getFullYear()} BookStore. Made with ❤️ for book lovers everywhere.
          </p>
          <p className="text-gray-400 text-sm">
            "The more that you read, the more things you will know. The more that you learn, the more places you'll go." - Dr. Seuss
          </p>
        </div>
      </footer>
    </div>
  );
};

export default BestWishes;
