import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Button, Table, Card, Form, Modal } from 'react-bootstrap';
import { FaTrash, FaEdit, FaPlus } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import Anavbar from './Anavbar';

const BookStore = () => {
  const [books, setBooks] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editBook, setEditBook] = useState(null);
  const [form, setForm] = useState({
    title: '',
    author: '',
    price: '',
    cover: '',
    description: ''
  });

  // Fetch all books
  useEffect(() => {
    fetchBooks();
  }, []);

  const fetchBooks = () => {
    axios.get('https://booknest-backend-55yh.onrender.com/books')
      .then(res => setBooks(res.data))
      .catch(err => console.error(err));
  };

  // Handle form input
  const handleChange = (e) => {
    const { name, value, files } = e.target;
    if (name === 'cover') {
      setForm({ ...form, cover: files[0] });
    } else {
      setForm({ ...form, [name]: value });
    }
  };

  // Add new book
  const handleAddBook = (e) => {
    e.preventDefault();
    const data = new FormData();
    data.append('title', form.title);
    data.append('author', form.author);
    data.append('price', form.price);
    data.append('description', form.description);
    if (form.cover) data.append('cover', form.cover);

    axios.post('https://booknest-backend-55yh.onrender.com/books', data)
      .then(() => {
        fetchBooks();
        setShowAddModal(false);
        setForm({ title: '', author: '', price: '', cover: '', description: '' });
      })
      .catch(err => alert('Failed to add book'));
  };

  // Delete book
  const handleDeleteBook = (id) => {
    if (window.confirm('Are you sure you want to delete this book?')) {
      axios.delete(`https://booknest-backend-55yh.onrender.com/books/${id}`)
        .then(() => fetchBooks())
        .catch(err => alert('Failed to delete book'));
    }
  };

  // Edit book
  const openEditModal = (book) => {
    setEditBook(book);
    setForm({
      title: book.title,
      author: book.author,
      price: book.price,
      cover: '',
      description: book.description
    });
    setShowEditModal(true);
  };

  const handleEditBook = (e) => {
    e.preventDefault();
    const data = new FormData();
    data.append('title', form.title);
    data.append('author', form.author);
    data.append('price', form.price);
    data.append('description', form.description);
    if (form.cover) data.append('cover', form.cover);

    axios.put(`http://localhost:4000/books/${editBook._id}`, data)
      .then(() => {
        fetchBooks();
        setShowEditModal(false);
        setEditBook(null);
        setForm({ title: '', author: '', price: '', cover: '', description: '' });
      })
      .catch(err => alert('Failed to update book'));
  };

  return (
    <div>
      <Anavbar />
      <br />
      <h1 className='text-center'>Book Store</h1>
      <div style={{ display: "flex", justifyContent: "center", marginBottom: 20 }}>
        <Button variant="success" onClick={() => setShowAddModal(true)}>
          <FaPlus /> Add Book
        </Button>
      </div>
      <div style={{ display: "flex", justifyContent: "center" }}>
        <Table striped bordered hover variant="dark" style={{ width: "90%" }}>
          <thead>
            <tr>
              <th>Sl/No</th>
              <th>Cover</th>
              <th>Title</th>
              <th>Author</th>
              <th>Description</th>
              <th>Price (₹)</th>
              <th>Operation</th>
            </tr>
          </thead>
          <tbody>
            {books.map((book, idx) => (
              <tr key={book._id}>
                <td>{idx + 1}</td>
                <td>
                  <img
                    src={book.cover ? `/covers/${book.cover}` : '/covers/default.jpg'}
                    alt={book.title}
                    style={{ width: 60, height: 90, objectFit: 'cover', borderRadius: 4 }}
                  />
                </td>
                <td>{book.title}</td>
                <td>{book.author}</td>
                <td style={{ maxWidth: 200, whiteSpace: 'pre-line' }}>{book.description}</td>
                <td>{book.price}</td>
                <td>
                  <Button
                    variant="outline-primary"
                    size="sm"
                    onClick={() => openEditModal(book)}
                    style={{ marginRight: 8 }}
                  >
                    <FaEdit />
                  </Button>
                  <Button
                    variant="outline-danger"
                    size="sm"
                    onClick={() => handleDeleteBook(book._id)}
                  >
                    <FaTrash />
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      </div>

      {/* Add Book Modal */}
      <Modal show={showAddModal} onHide={() => setShowAddModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Add New Book</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleAddBook} encType="multipart/form-data">
          <Modal.Body>
            <Form.Group>
              <Form.Label>Title</Form.Label>
              <Form.Control
                name="title"
                value={form.title}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Author</Form.Label>
              <Form.Control
                name="author"
                value={form.author}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Price (₹)</Form.Label>
              <Form.Control
                name="price"
                type="number"
                value={form.price}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                name="description"
                value={form.description}
                onChange={handleChange}
                rows={3}
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Cover Image</Form.Label>
              <Form.Control
                name="cover"
                type="file"
                accept="image/*"
                onChange={handleChange}
              />
              <Form.Text className="text-muted">
                Place your book cover images in the <b>public/covers</b> folder.
              </Form.Text>
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowAddModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              Add Book
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Edit Book Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Edit Book</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleEditBook} encType="multipart/form-data">
          <Modal.Body>
            <Form.Group>
              <Form.Label>Title</Form.Label>
              <Form.Control
                name="title"
                value={form.title}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Author</Form.Label>
              <Form.Control
                name="author"
                value={form.author}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Price (₹)</Form.Label>
              <Form.Control
                name="price"
                type="number"
                value={form.price}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                name="description"
                value={form.description}
                onChange={handleChange}
                rows={3}
              />
            </Form.Group>
            <Form.Group>
              <Form.Label>Cover Image</Form.Label>
              <Form.Control
                name="cover"
                type="file"
                accept="image/*"
                onChange={handleChange}
              />
              <Form.Text className="text-muted">
                Leave blank to keep current cover.
              </Form.Text>
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowEditModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              Save Changes
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </div>
  );
};

export default BookStore;
