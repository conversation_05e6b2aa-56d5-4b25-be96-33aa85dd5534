// Backend API Configuration
// Toggle between local and deployed backend
const IS_PRODUCTION = true; // Set to false for local development

export const API_BASE_URL = IS_PRODUCTION
  ? 'https://booknest-backend-55yh.onrender.com'
  : 'http://localhost:4000';

// API Endpoints
export const API_ENDPOINTS = {
  // User endpoints
  USER_LOGIN: `${API_BASE_URL}/login`,
  USER_REGISTER: `${API_BASE_URL}/register`,
  USER_ORDERS: `${API_BASE_URL}/getuserorders`,
  
  // Seller endpoints
  SELLER_LOGIN: `${API_BASE_URL}/slogin`,
  SELLER_REGISTER: `${API_BASE_URL}/sregister`,
  SELLER_ITEMS: `${API_BASE_URL}/getitem`,
  SELLER_ORDERS: `${API_BASE_URL}/getsellerorders`,
  
  // Admin endpoints
  ADMIN_LOGIN: `${API_BASE_URL}/alogin`,
  ADMIN_REGISTER: `${API_BASE_URL}/aregister`,
  ADMIN_USERS: `${API_BASE_URL}/users`,
  ADMIN_SELLERS: `${API_BASE_URL}/sellers`,
  
  // Items endpoints
  ITEMS: `${API_BASE_URL}/item`,
  ADD_ITEM: `${API_BASE_URL}/additem`,
  DELETE_ITEM: `${API_BASE_URL}/itemdelete`,
  
  // Orders endpoints
  ORDERS: `${API_BASE_URL}/orders`,
  ADD_ORDER: `${API_BASE_URL}/addorder`,
  
  // Wishlist endpoints
  WISHLIST: `${API_BASE_URL}/wishlist`,
  ADD_WISHLIST: `${API_BASE_URL}/addwishlist`,
  
  // Feedback endpoints
  FEEDBACK: `${API_BASE_URL}/feedback`,
  ADD_FEEDBACK: `${API_BASE_URL}/addfeedback`
};

export default API_BASE_URL;
