/* BookStore.css - Styles for Book Store Website */

/* General Layout */
body {
  font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
  background: linear-gradient(120deg, #f0fdfa 0%, #fef3c7 100%);
  margin: 0;
  min-height: 100vh;
}

/* Navbar */
.bookstore-navbar {
  background: #14b8a6;
  color: #fff;
  padding: 1rem 0;
  box-shadow: 0 2px 8px rgba(20,184,166,0.08);
}
.bookstore-navbar .navbar-brand {
  font-size: 2rem;
  font-weight: bold;
  color: #fff !important;
  letter-spacing: 1px;
}
.bookstore-navbar .btn {
  margin-left: 0.5rem;
}

/* Book List */
.bookstore-card {
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  transition: transform 0.15s;
  background: #fff;
}
.bookstore-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 6px 20px rgba(20,184,166,0.10);
}
.bookstore-card .card-img-top {
  border-radius: 10px 10px 0 0;
  height: 250px;
  object-fit: cover;
  background: #f0fdfa;
}
.bookstore-card .btn-primary {
  background: #14b8a6;
  border: none;
  transition: background 0.2s;
}
.bookstore-card .btn-primary:hover {
  background: #0f766e;
}

/* Book Details */
.book-details-img {
  width: 100%;
  max-width: 350px;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(20,184,166,0.08);
  object-fit: cover;
  background: #f0fdfa;
}
.book-details-content h2 {
  color: #0f766e;
  font-weight: bold;
}
.book-details-content p {
  font-size: 1.1rem;
  color: #444;
}
.book-details-content .btn-success {
  background: #f97316;
  border: none;
}
.book-details-content .btn-success:hover {
  background: #15803d;
}

/* Orders */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin: 2rem auto;
  max-width: 900px;
}
.order-card {
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  padding: 1.5rem;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  display: flex;
  align-items: center;
}
.order-card .order-image {
  width: 100px;
  height: 140px;
  object-fit: cover;
  border-radius: 6px;
  margin-right: 1.5rem;
  background: #f3f4f8;
}
.order-card .order-info {
  flex: 1;
}
.order-card h5 {
  margin: 0 0 0.5rem 0;
  color: #4f46e5;
  font-weight: bold;
}
.order-card p {
  margin: 0.2rem 0;
  color: #444;
  font-size: 1rem;
}
.order-card .order-status {
  font-weight: bold;
  color: #22c55e;
}
.order-card .order-status.processing {
  color: #f59e42;
}
.order-card .order-status.failed {
  color: #ef4444;
}

/* Footer */
.bookstore-footer {
  background: #f3f4f8;
  color: #888;
  text-align: center;
  padding: 1.5rem 0 1rem 0;
  font-size: 1rem;
  margin-top: 3rem;
  border-top: 1px solid #e0e0e0;
}

/* Responsive Design */
@media (max-width: 900px) {
  .order-list {
    max-width: 100%;
    padding: 0 1rem;
  }
}
@media (max-width: 700px) {
  .order-card {
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;
  }
  .order-card .order-image {
    margin-bottom: 1rem;
    margin-right: 0;
    width: 80px;
    height: 110px;
  }
}
@media (max-width: 600px) {
  .bookstore-card .card-img-top {
    height: 180px;
  }
  .order-list {
    padding: 0 0.5rem;
  }
  .order-card {
    padding: 0.7rem;
  }
  .order-card .order-image {
    width: 60px;
    height: 80px;
  }
}

/* Utility */
.text-center {
  text-align: center;
}
.mt-4 { margin-top: 1.5rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mt-5 { margin-top: 2.5rem; }
.mb-5 { margin-bottom: 2.5rem; }