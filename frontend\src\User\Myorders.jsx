import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { <PERSON>, Button } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { FaClipboardList, FaSignOutAlt, FaHome } from 'react-icons/fa';
import './BookStore.css'; // Create this CSS file for custom styles
import Footer from '../Components/Footer';

// Dark Academia Navbar for Book Store
function BookStoreNavbar() {
  const navigate = useNavigate();
  const user = JSON.parse(localStorage.getItem('user'));

  const handleLogout = () => {
    localStorage.removeItem('user');
    alert('Logged out successfully!');
    navigate('/');
  };

  return (
    <nav className="bg-gradient-to-r from-amber-900 to-orange-800 shadow-lg">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FaClipboardList className="text-amber-200 text-2xl" />
            <h1 className="text-2xl font-bold text-amber-100 font-serif-primary">My Orders</h1>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => navigate('/books')}
              className="flex items-center space-x-2 px-4 py-2 text-amber-100 hover:text-white hover:bg-amber-700 rounded-lg transition-all font-sans"
            >
              <FaHome className="text-sm" />
              <span>Browse Books</span>
            </button>
            {user ? (
              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 bg-red-100 text-red-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-red-200 transition-colors"
              >
                <FaSignOutAlt />
                <span>Logout</span>
              </button>
            ) : (
              <button
                onClick={() => navigate('/login')}
                className="flex items-center space-x-2 bg-green-100 text-green-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-green-200 transition-colors"
              >
                <span>Login</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}

// Book List Page
function BookList() {
  const [books, setBooks] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    axios.get('https://booknest-backend-55yh.onrender.com/books')
      .then(res => setBooks(res.data))
      .catch(err => console.error('Error fetching books:', err));
  }, []);

  return (
    <div className="container mt-4">
      <h1 className="text-center mb-4">Welcome to the Book Store</h1>
      <div className="row">
        {books.map(book => (
          <div className="col-md-3 mb-4" key={book._id}>
            <Card className="h-100 bookstore-card">
              <Card.Img variant="top" src={`/${book.coverImage}`} alt={book.title} style={{ height: '250px', objectFit: 'cover' }} />
              <Card.Body>
                <Card.Title>{book.title}</Card.Title>
                <Card.Text>
                  <strong>Author:</strong> {book.author}<br />
                  <strong>Price:</strong> ${book.price}
                </Card.Text>
                <Button variant="primary" onClick={() => navigate(`/book/${book._id}`)}>View Details</Button>
              </Card.Body>
            </Card>
          </div>
        ))}
      </div>
    </div>
  );
}

// Book Details Page
function BookDetails({ bookId }) {
  const [book, setBook] = useState(null);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    axios.get(`https://booknest-backend-55yh.onrender.com/books/${bookId}`)
      .then(res => setBook(res.data))
      .catch(err => console.error('Error fetching book:', err));
  }, [bookId]);

  const handleOrder = () => {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user) {
      navigate('/login');
      return;
    }
    axios.post('https://booknest-backend-55yh.onrender.com/order', {
      userId: user.id,
      bookId: book._id
    })
      .then(() => setOrderSuccess(true))
      .catch(err => alert('Order failed!'));
  };

  if (!book) return <div className="container mt-5">Loading...</div>;

  return (
    <div className="container mt-5">
      <div className="row">
        <div className="col-md-4">
          <img src={`/${book.coverImage}`} alt={book.title} className="img-fluid" />
        </div>
        <div className="col-md-8">
          <h2>{book.title}</h2>
          <p><strong>Author:</strong> {book.author}</p>
          <p><strong>Description:</strong> {book.description}</p>
          <p><strong>Price:</strong> ${book.price}</p>
          <Button variant="success" onClick={handleOrder}>Order Now</Button>
          {orderSuccess && <div className="alert alert-success mt-3">Order placed successfully!</div>}
        </div>
      </div>
    </div>
  );
}

// My Orders Page
function MyOrders() {
  const [orders, setOrders] = useState([]);
  const [books, setBooks] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user) {
      navigate('/login');
      return;
    }
    axios.get(`https://booknest-backend-55yh.onrender.com/getorders/${user.id}`)
      .then(res => {
        setOrders(res.data);
        // Fetch all books for mapping
        axios.get('https://booknest-backend-55yh.onrender.com/books')
          .then(bookRes => {
            const bookMap = {};
            bookRes.data.forEach(b => { bookMap[b._id] = b; });
            setBooks(bookMap);
          });
      })
      .catch(err => {
        setError('Failed to load orders.');
        setLoading(false);
      });
  }, [navigate]);

  if (loading) return <div className="text-center py-5">Loading orders...</div>;
  if (error) return <div className="text-danger text-center py-5">{error}</div>;
  if (orders.length === 0) return <div className="container py-5 text-center">No orders found.</div>;

  return (
    <div>
      <BookStoreNavbar />
      <div className="container py-5">
        <h2 className="mb-4 text-center" style={{fontFamily: 'Playfair Display, serif'}}>My Orders</h2>
        <div className="row g-4">
          {orders.map((order) => (
            <div className="col-12 col-md-6 col-lg-4" key={order._id}>
              <div className="card h-100 shadow-sm">
                <div className="card-body d-flex flex-column">
                  <h5 className="card-title" style={{fontFamily: 'Playfair Display, serif'}}>Order #{order._id.slice(-6)}</h5>
                  <p className="card-text">Date: {new Date(order.createdAt).toLocaleDateString()}</p>
                  <ul className="list-unstyled flex-grow-1">
                    {order.books.map((book, idx) => (
                      <li key={idx} className="d-flex align-items-center mb-2">
                        <img
                          src={books[book.bookId] ? `/${books[book.bookId].coverImage}` : '/public/default_cover.svg'}
                          alt={books[book.bookId] ? books[book.bookId].title : 'Book'}
                          style={{width: 40, height: 60, objectFit: 'cover', marginRight: 10, borderRadius: 4}}
                          onError={(e) => { e.target.onerror = null; e.target.src = '/public/default_cover.svg'; }}
                        />
                        <span>{books[book.bookId] ? books[book.bookId].title : 'Book'}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <Footer />
    </div>
  );
}

// Routing Setup
import { BrowserRouter as Router, Routes, Route, useParams } from 'react-router-dom';

function BookDetailsWrapper() {
  const { id } = useParams();
  return <BookDetails bookId={id} />;
}

function App() {
  return (
    <Router>
      <BookStoreNavbar />
      <Routes>
        <Route path="/" element={<BookList />} />
        <Route path="/book/:id" element={<BookDetailsWrapper />} />
        <Route path="/myorders" element={<MyOrders />} />
        {/* Add login/register routes as needed */}
      </Routes>
      <Footer />
    </Router>
  );
}

export default App;
