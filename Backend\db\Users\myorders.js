const mongoose = require('mongoose');

const bookschema = new mongoose.Schema({
    flatno:String,
    pincode:String,
    city:String,
    state:String,
    totalamount:String,
    seller:String,
    sellerId:String,
    booktitle:String,
    bookauthor:String,
    bookgenre:String,
    itemImage:String,
    description:String,
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    userName:String,
    BookingDate: {
        type: Date, // Store as proper Date objects for better handling
        default: Date.now
    },
    Delivery: {
        type: Date, // Store as proper Date objects for better handling
        default: () => {
            // Set delivery date to 7 days from now
            const deliveryDate = new Date();
            deliveryDate.setDate(deliveryDate.getDate() + 7);
            return deliveryDate;
        }
    }
    
    
    
})

module.exports =mongoose.model('myorders',bookschema)
