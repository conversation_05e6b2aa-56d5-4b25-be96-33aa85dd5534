const mongoose = require('mongoose');

const bookschema = new mongoose.Schema({
    flatno:String,
    pincode:String,
    city:String,
    state:String,
    totalamount:String,
    seller:String,
    sellerId:String,
    booktitle:String,
    bookauthor:String,
    bookgenre:String,
    itemImage:String,
    description:String,
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    userName:String,
    BookingDate: {
        type: String, // Store dates as strings
        default: () => {
            // Set the default value to the current date in DD/MM/YYYY format
            const currentDate = new Date();
            const day = String(currentDate.getDate()).padStart(2, '0');
            const month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Month is zero-based, so add 1
            const year = currentDate.getFullYear();
            return `${day}/${month}/${year}`;
        }
    },
    Delivery: {
      type: String, // Store dates as strings
      default: () => {
        // Set the default value to the current date + 7 days in DD/MM/YYYY format
        const currentDate = new Date();
        currentDate.setDate(currentDate.getDate() + 7); // Add 7 days
        const day = String(currentDate.getDate()).padStart(2, '0');
        const month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Month is zero-based, so add 1
        const year = currentDate.getFullYear();
        return `${day}/${month}/${year}`;
      }
    }
    
    
    
})

module.exports =mongoose.model('myorders',bookschema)
