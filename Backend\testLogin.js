const axios = require('axios');

const BASE_URL = 'http://localhost:4000';

async function testLogin() {
  console.log('🧪 Testing Login Functionality...\n');

  // Test Customer Login
  console.log('1️⃣ Testing Customer Login...');
  try {
    const customerResponse = await axios.post(`${BASE_URL}/login`, {
      email: '<EMAIL>',
      password: 'demo123'
    });
    console.log('✅ Customer Login Response:', customerResponse.data);
  } catch (error) {
    console.log('❌ Customer Login Error:', error.response?.data || error.message);
  }

  // Test Seller Login
  console.log('\n2️⃣ Testing Seller Login...');
  try {
    const sellerResponse = await axios.post(`${BASE_URL}/slogin`, {
      email: '<EMAIL>',
      password: 'seller123'
    });
    console.log('✅ Seller Login Response:', sellerResponse.data);
  } catch (error) {
    console.log('❌ Seller Login Error:', error.response?.data || error.message);
  }

  // Test Admin Login
  console.log('\n3️⃣ Testing Admin Login...');
  try {
    const adminResponse = await axios.post(`${BASE_URL}/alogin`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    console.log('✅ Admin Login Response:', adminResponse.data);
  } catch (error) {
    console.log('❌ Admin Login Error:', error.response?.data || error.message);
  }

  // Test Invalid Credentials
  console.log('\n4️⃣ Testing Invalid Credentials...');
  try {
    const invalidResponse = await axios.post(`${BASE_URL}/login`, {
      email: '<EMAIL>',
      password: 'wrongpassword'
    });
    console.log('⚠️ Invalid Login Response:', invalidResponse.data);
  } catch (error) {
    console.log('✅ Invalid Login Correctly Rejected:', error.response?.data || error.message);
  }

  console.log('\n🎉 Login tests completed!');
}

testLogin().catch(console.error);
