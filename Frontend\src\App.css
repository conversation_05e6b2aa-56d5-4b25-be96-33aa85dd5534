/* BookNest Global Styles */
* {
  box-sizing: border-box;
}

#root {
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Global Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.btn-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 600;
  color: white;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
  background: linear-gradient(135deg, #e879f9 0%, #ec4899 100%);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 600;
  color: white;
  transition: all 0.2s ease;
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
  color: white;
}

/* Global Card Styles */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: none;
  transition: all 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
  border-bottom: 1px solid #e2e8f0;
  border-radius: 12px 12px 0 0 !important;
  padding: 20px;
  font-weight: 600;
  color: #2d3748;
}

.card-body {
  padding: 24px;
}

/* Global Form Styles */
.form-control {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  transition: all 0.2s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-label {
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8px;
}

/* Global Navigation Styles */
.navbar {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-weight: 700;
  font-size: 24px;
}

.nav-link {
  font-weight: 500;
  transition: all 0.2s ease;
}

/* Global Alert Styles */
.alert {
  border: none;
  border-radius: 8px;
  padding: 16px;
  font-weight: 500;
}

.alert-success {
  background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
  color: #2f855a;
}

.alert-danger {
  background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
  color: #c53030;
}

.alert-warning {
  background: linear-gradient(135deg, #feebc8 0%, #fbd38d 100%);
  color: #c05621;
}

.alert-info {
  background: linear-gradient(135deg, #bee3f8 0%, #90cdf4 100%);
  color: #2b6cb0;
}

/* Global Table Styles */
.table {
  border-collapse: collapse;
  width: 100%;
}

.table th {
  background: #f8fafc;
  font-weight: 600;
  color: #4a5568;
  padding: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.table td {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  color: #2d3748;
}

.table-hover tbody tr:hover {
  background: #f8fafc;
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-custom {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #667eea, #764ba2) border-box;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-body {
    padding: 16px;
  }

  .btn {
    width: 100%;
    margin-bottom: 8px;
  }
}
