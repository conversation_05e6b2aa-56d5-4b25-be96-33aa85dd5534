{"name": "booknest", "version": "1.0.0", "description": "Complete Bookstore Website with React Frontend and Node.js Backend", "main": "Backend/server.js", "scripts": {"start": "cd Backend && npm start", "dev": "concurrently \"cd Backend && npm run dev\" \"cd frontend && npm run dev\"", "build": "cd frontend && npm run build", "build:frontend": "cd frontend && npm install && npm run build", "build:backend": "cd Backend && npm install", "install:all": "npm install && cd Backend && npm install && cd ../frontend && npm install", "install:frontend": "cd frontend && npm install", "install:backend": "cd Backend && npm install", "start:frontend": "cd frontend && npm start", "start:backend": "cd Backend && npm start", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["bookstore", "react", "nodejs", "mongodb", "express", "ecommerce", "books"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/NarendraTalari/BookNest.git"}, "homepage": "https://github.com/NarendraTalari/BookNest#readme", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "Backend"]}