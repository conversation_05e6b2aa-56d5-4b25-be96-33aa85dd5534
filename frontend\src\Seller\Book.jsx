
import React, { useEffect, useState } from 'react';
import { useParams, Link } from 'react-router-dom';

// Simple Navbar for Book Store
const Navbar = () => (
  <nav style={{ background: '#333', color: '#fff', padding: '1rem', display: 'flex', justifyContent: 'space-between' }}>
    <div>
      <Link to="/" style={{ color: '#fff', textDecoration: 'none', fontWeight: 'bold', fontSize: '1.5rem' }}>BookStore</Link>
    </div>
    <div>
      <Link to="/books" style={{ color: '#fff', marginRight: '1rem', textDecoration: 'none' }}>Books</Link>
      <Link to="/cart" style={{ color: '#fff', textDecoration: 'none' }}>Cart</Link>
    </div>
  </nav>
);

// Book Details Page
const Book = () => {
  const { id } = useParams();
  const [book, setBook] = useState(null);

  useEffect(() => {
    // Fetch book details from backend
    fetch(`https://booknest-backend-55yh.onrender.com/books/${id}`)
      .then(res => res.json())
      .then(data => setBook(data))
      .catch(() => setBook(null));
  }, [id]);

  if (!book) {
    return (
      <div>
        <Navbar />
        <div style={{ textAlign: 'center', marginTop: '2rem' }}>
          <h2>Loading book details...</h2>
        </div>
      </div>
    );
  }

  // Assume book.coverImage is the filename in public/covers/
  const coverSrc = book.coverImage
    ? `/covers/${book.coverImage}`
    : '/covers/default.jpg';

  return (
    <div>
      <Navbar />
      <div style={{ maxWidth: 900, margin: '2rem auto', background: '#fff', borderRadius: 8, boxShadow: '0 2px 8px #ccc', padding: 32 }}>
        <div style={{ display: 'flex', gap: 32 }}>
          <div style={{ flex: '0 0 300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <img
              src={coverSrc}
              alt={book.title}
              style={{ width: 220, height: 320, objectFit: 'cover', borderRadius: 8, boxShadow: '0 2px 8px #bbb' }}
            />
          </div>
          <div style={{ flex: 1 }}>
            <h1 style={{ fontSize: '2.2rem', marginBottom: 8 }}>{book.title}</h1>
            <h3 style={{ color: '#666', marginBottom: 16 }}>by {book.author}</h3>
            <p style={{ fontSize: '1.1rem', marginBottom: 24 }}>{book.description}</p>
            <div style={{ marginBottom: 16 }}>
              <span style={{ fontWeight: 'bold', fontSize: '1.2rem' }}>Price: </span>
              <span style={{ fontSize: '1.2rem', color: '#2a7' }}>${book.price}</span>
            </div>
            <div style={{ marginBottom: 16 }}>
              <span style={{ fontWeight: 'bold' }}>Category: </span>
              <span>{book.category}</span>
            </div>
            <div style={{ marginBottom: 16 }}>
              <span style={{ fontWeight: 'bold' }}>ISBN: </span>
              <span>{book.isbn}</span>
            </div>
            <button
              style={{
                background: '#2a7',
                color: '#fff',
                border: 'none',
                padding: '0.75rem 2rem',
                borderRadius: 4,
                fontSize: '1.1rem',
                cursor: 'pointer',
                marginTop: 16
              }}
              onClick={() => alert('Added to cart!')}
            >
              Add to Cart
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Book;
