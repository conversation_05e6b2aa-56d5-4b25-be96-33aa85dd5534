import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaShoppingCart, FaClipboardList } from 'react-icons/fa';

const FloatingActions = () => {
  const [cartCount, setCartCount] = useState(0);
  const navigate = useNavigate();

  useEffect(() => {
    // Get cart count from localStorage
    const updateCounts = () => {
      try {
        const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
        setCartCount(cartItems.length);
      } catch (error) {
        console.error('Error reading from localStorage:', error);
        setCartCount(0);
      }
    };

    updateCounts();

    // Listen for storage changes
    const handleStorageChange = () => {
      updateCounts();
    };

    window.addEventListener('storage', handleStorageChange);
    
    // Custom event for cart updates
    window.addEventListener('cartUpdated', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('cartUpdated', handleStorageChange);
    };
  }, []);

  const handleCartClick = () => {
    navigate('/mycart');
  };

  const handleOrdersClick = () => {
    navigate('/myorders-new');
  };

  return (
    <>
    <div className="floating-actions">
      {/* Cart Button */}
      <button 
        className="fab cart-fab"
        onClick={handleCartClick}
        title="My Cart"
      >
        <FaShoppingCart />
        {cartCount > 0 && (
          <span className="fab-badge">
            {cartCount > 99 ? '99+' : cartCount}
          </span>
        )}
      </button>

      {/* Orders Button */}
      <button
        className="fab orders-fab"
        onClick={handleOrdersClick}
        title="My Orders"
      >
        <FaClipboardList />
      </button>
    </div>
  </>
  );
};

export default FloatingActions;
