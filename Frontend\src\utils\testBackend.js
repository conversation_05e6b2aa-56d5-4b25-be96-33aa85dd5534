// Backend Connection Test Utility
import { API_BASE_URL, API_ENDPOINTS } from '../config/api.js';

// Test backend connection
export const testBackendConnection = async () => {
  try {
    console.log('🔍 Testing backend connection...');
    console.log('Backend URL:', API_BASE_URL);
    
    // Test basic connectivity
    const response = await fetch(`${API_BASE_URL}/item`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('✅ Backend connection successful!');
    console.log(`📚 Found ${data.length} books in database`);
    
    return {
      success: true,
      message: 'Backend connection successful',
      booksCount: data.length,
      data: data
    };
    
  } catch (error) {
    console.error('❌ Backend connection failed:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
};

// Test login endpoint
export const testLoginEndpoint = async (credentials) => {
  try {
    console.log('🔐 Testing login endpoint...');
    
    const response = await fetch(API_ENDPOINTS.USER_LOGIN, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials)
    });
    
    const data = await response.json();
    
    if (response.ok && data.Status === 'Success') {
      console.log('✅ Login test successful!');
      return {
        success: true,
        message: 'Login endpoint working',
        data: data
      };
    } else {
      console.log('⚠️ Login test failed:', data.Error || 'Unknown error');
      return {
        success: false,
        message: data.Error || 'Login failed',
        data: data
      };
    }
    
  } catch (error) {
    console.error('❌ Login endpoint test failed:', error);
    return {
      success: false,
      message: error.message,
      error: error
    };
  }
};

// Test all endpoints
export const testAllEndpoints = async () => {
  console.log('🧪 Running comprehensive backend tests...');
  
  const results = {
    connection: await testBackendConnection(),
    login: await testLoginEndpoint({
      email: '<EMAIL>',
      password: 'demo123'
    })
  };
  
  console.log('📊 Test Results:', results);
  return results;
};

// Export for use in components
export default {
  testBackendConnection,
  testLoginEndpoint,
  testAllEndpoints
};
