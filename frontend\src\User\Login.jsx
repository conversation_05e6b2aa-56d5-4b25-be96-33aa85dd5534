import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Form, Button, Alert, Tab, Tabs } from 'react-bootstrap';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, <PERSON>aEye<PERSON>lash, <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Fa<PERSON>ser<PERSON>ie, FaUserShield, FaArrowLeft } from 'react-icons/fa';
import axios from 'axios';

// Set your backend login endpoint here
const LOGIN_ENDPOINT = 'https://booknest-backend-55yh.onrender.com/api/auth/login';

// Demo credentials for different user types
const DEMO_CREDENTIALS = {
  user: { email: '<EMAIL>', password: 'demo123' },
  seller: { email: '<EMAIL>', password: 'seller123' }
};

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [userType, setUserType] = useState('user');
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isSignup, setIsSignup] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async (e) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    // Basic validation
    if (!email.trim() || !password.trim()) {
      setError('Please fill in all fields');
      setLoading(false);
      return;
    }

    try {
      // For demo purposes, check demo credentials first
      const demoCredentials = DEMO_CREDENTIALS[userType];
      if (email === demoCredentials.email && password === demoCredentials.password) {
        // Demo login success
        const demoUser = {
          id: userType === 'admin' ? 'admin-1' : `${userType}-demo`,
          email: email,
          name: userType === 'admin' ? 'Talari Narendra' : `Demo ${userType.charAt(0).toUpperCase() + userType.slice(1)}`,
          userType: userType
        };

        localStorage.setItem('token', 'demo-token-' + userType);
        localStorage.setItem('user', JSON.stringify(demoUser));
        localStorage.setItem('userType', userType);

        // Navigate based on user type
        if (userType === 'admin') {
          navigate('/admin');
        } else if (userType === 'seller') {
          navigate('/seller');
        } else {
          navigate('/user-home');
        }
        return;
      }

      // If not demo credentials, try backend authentication
      const res = await fetch(LOGIN_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password, userType }),
      });
      const data = await res.json();
      if (res.ok && data.token) {
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));
        localStorage.setItem('userType', userType);

        // Navigate based on user type
        if (userType === 'admin') {
          navigate('/admin');
        } else if (userType === 'seller') {
          navigate('/seller');
        } else {
          navigate('/user-home');
        }
      } else {
        console.error('Login failed response:', data);
        setError(data.message || 'Invalid credentials. Try demo credentials.');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('Login failed. Try demo credentials or check your connection.');
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = (type) => {
    const credentials = DEMO_CREDENTIALS[type];
    setEmail(credentials.email);
    setPassword(credentials.password);
    setUserType(type);
  };

  const handleSignup = async (e) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    // Basic validation
    if (!email.trim() || !password.trim()) {
      setError('Please fill in all fields');
      setLoading(false);
      return;
    }

    try {
      // For demo purposes, simulate signup success
      const newUser = {
        id: `${userType}-${Date.now()}`,
        email: email,
        name: email.split('@')[0],
        userType: userType
      };

      localStorage.setItem('token', 'demo-token-' + userType);
      localStorage.setItem('user', JSON.stringify(newUser));
      localStorage.setItem('userType', userType);

      // Navigate based on user type
      if (userType === 'seller') {
        navigate('/seller');
      } else {
        navigate('/user-home');
      }
    } catch (err) {
      console.error('Signup error:', err);
      setError('Signup failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Container>
        <Row className="justify-content-center">
          <Col md={8} lg={6} xl={5}>
            <Card className="login-card shadow-lg">
              <Card.Body className="p-4">
                {/* Header */}
                <div className="login-header">
                  <div className="login-icon">
                    <FaBook size={32} color="white" />
                  </div>
                  <h2 className="h3 fw-bold mb-2" style={{ color: '#333', fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif' }}>
                    Welcome to BookNest
                  </h2>
                  <p className="mb-0" style={{ color: '#666', fontSize: '14px' }}>Sign in to your account</p>
                </div>

                {/* Demo Credentials Display */}
                <div className="demo-credentials">
                  <div className="fw-bold mb-2" style={{ color: '#FF6600', fontSize: '0.9rem' }}>Demo Credentials:</div>
                  <div className="demo-credential-row">
                    <span style={{ color: '#333', fontWeight: '500' }}>User:</span>
                    <span className="demo-credential-code"><EMAIL> / demo123</span>
                  </div>
                  <div className="demo-credential-row">
                    <span style={{ color: '#333', fontWeight: '500' }}>Seller:</span>
                    <span className="demo-credential-code"><EMAIL> / seller123</span>
                  </div>
                </div>

                {/* Login/Signup Tabs */}
                <Tabs
                  activeKey={isSignup ? 'signup' : 'login'}
                  onSelect={(k) => setIsSignup(k === 'signup')}
                  className="mb-3"
                  fill
                >
                  <Tab eventKey="login" title="Sign In">
                    <Form onSubmit={handleLogin}>
                      {/* User Type Selection */}
                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold" style={{ color: '#333', fontSize: '14px' }}>Login as:</Form.Label>
                        <div className="d-flex gap-2">
                          <Button
                            variant={userType === 'user' ? 'primary' : 'outline-primary'}
                            size="sm"
                            onClick={() => setUserType('user')}
                            className="flex-fill"
                            style={{
                              backgroundColor: userType === 'user' ? '#FF6600' : 'transparent',
                              borderColor: '#FF6600',
                              color: userType === 'user' ? 'white' : '#FF6600',
                              fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                              fontSize: '13px'
                            }}
                          >
                            <FaUser className="me-1" /> User
                          </Button>
                          <Button
                            variant={userType === 'seller' ? 'primary' : 'outline-primary'}
                            size="sm"
                            onClick={() => setUserType('seller')}
                            className="flex-fill"
                            style={{
                              backgroundColor: userType === 'seller' ? '#FF6600' : 'transparent',
                              borderColor: '#FF6600',
                              color: userType === 'seller' ? 'white' : '#FF6600',
                              fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                              fontSize: '13px'
                            }}
                          >
                            <FaUserTie className="me-1" /> Seller
                          </Button>
                          <Button
                            variant={userType === 'admin' ? 'primary' : 'outline-primary'}
                            size="sm"
                            onClick={() => setUserType('admin')}
                            className="flex-fill"
                            style={{
                              backgroundColor: userType === 'admin' ? '#FF6600' : 'transparent',
                              borderColor: '#FF6600',
                              color: userType === 'admin' ? 'white' : '#FF6600',
                              fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                              fontSize: '13px'
                            }}
                          >
                            <FaUserShield className="me-1" /> Admin
                          </Button>
                        </div>
                      </Form.Group>

                      {/* Email Input */}
                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold" style={{ color: '#333', fontSize: '14px' }}>
                          {userType === 'admin' ? 'Username' : 'Email'}
                        </Form.Label>
                        <Form.Control
                          type={userType === 'admin' ? 'text' : 'email'}
                          placeholder={userType === 'admin' ? 'Enter username' : 'Enter your email'}
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="login-input"
                          required
                        />
                      </Form.Group>

                      {/* Password Input */}
                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold" style={{ color: '#333', fontSize: '14px' }}>Password</Form.Label>
                        <div className="position-relative">
                          <Form.Control
                            type={showPassword ? 'text' : 'password'}
                            placeholder="Enter your password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            className="login-input"
                            required
                          />
                          <Button
                            variant="link"
                            className="position-absolute end-0 top-50 translate-middle-y"
                            style={{ border: 'none', color: '#FF6600', padding: '0 8px' }}
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? <FaEyeSlash /> : <FaEye />}
                          </Button>
                        </div>
                      </Form.Group>

                      {error && <Alert variant="danger" className="py-2">{error}</Alert>}

                      {/* Login Button */}
                      <Button
                        type="submit"
                        className="login-button w-100 mb-3"
                        disabled={loading}
                      >
                        {loading ? 'Signing In...' : 'Sign In'}
                      </Button>

                      {/* Demo Login Buttons */}
                      <div className="d-flex gap-2 mb-3">
                        <Button
                          variant="outline-secondary"
                          size="sm"
                          onClick={() => handleDemoLogin('user')}
                          className="flex-fill"
                          style={{
                            borderColor: '#ddd',
                            color: '#666',
                            fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                            fontSize: '13px'
                          }}
                        >
                          Demo User
                        </Button>
                        <Button
                          variant="outline-secondary"
                          size="sm"
                          onClick={() => handleDemoLogin('seller')}
                          className="flex-fill"
                          style={{
                            borderColor: '#ddd',
                            color: '#666',
                            fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
                            fontSize: '13px'
                          }}
                        >
                          Demo Seller
                        </Button>
                        {userType === 'admin' && (
                          <Button
                            variant="outline-secondary"
                            size="sm"
                            onClick={() => handleDemoLogin('admin')}
                            className="flex-fill"
                          >
                            Admin
                          </Button>
                        )}
                      </div>
                    </Form>
                  </Tab>
                  <Tab eventKey="signup" title="Sign Up">
                    <Form onSubmit={handleSignup}>
                      {/* User Type Selection for Signup */}
                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold">Sign up as:</Form.Label>
                        <div className="d-flex gap-2">
                          <Button
                            variant={userType === 'user' ? 'primary' : 'outline-primary'}
                            size="sm"
                            onClick={() => setUserType('user')}
                            className="flex-fill"
                            style={{
                              backgroundColor: userType === 'user' ? '#8b4513' : 'transparent',
                              borderColor: '#8b4513',
                              color: userType === 'user' ? 'white' : '#8b4513'
                            }}
                          >
                            <FaUser className="me-1" /> User
                          </Button>
                          <Button
                            variant={userType === 'seller' ? 'primary' : 'outline-primary'}
                            size="sm"
                            onClick={() => setUserType('seller')}
                            className="flex-fill"
                            style={{
                              backgroundColor: userType === 'seller' ? '#8b4513' : 'transparent',
                              borderColor: '#8b4513',
                              color: userType === 'seller' ? 'white' : '#8b4513'
                            }}
                          >
                            <FaUserTie className="me-1" /> Seller
                          </Button>
                        </div>
                        <small className="text-muted">Note: Admin accounts cannot be created through signup</small>
                      </Form.Group>

                      {/* Email Input */}
                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold">Email</Form.Label>
                        <Form.Control
                          type="email"
                          placeholder="Enter your email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="login-input"
                          required
                        />
                      </Form.Group>

                      {/* Password Input */}
                      <Form.Group className="mb-3">
                        <Form.Label className="fw-semibold">Password</Form.Label>
                        <div className="position-relative">
                          <Form.Control
                            type={showPassword ? 'text' : 'password'}
                            placeholder="Create a password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            className="login-input"
                            required
                          />
                          <Button
                            variant="link"
                            className="position-absolute end-0 top-50 translate-middle-y"
                            style={{ border: 'none', color: '#8b4513' }}
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? <FaEyeSlash /> : <FaEye />}
                          </Button>
                        </div>
                      </Form.Group>

                      {error && <Alert variant="danger" className="py-2">{error}</Alert>}

                      {/* Signup Button */}
                      <Button
                        type="submit"
                        className="login-button w-100"
                        disabled={loading}
                      >
                        {loading ? 'Creating Account...' : 'Create Account'}
                      </Button>
                    </Form>
                  </Tab>
                </Tabs>

                {/* Footer */}
                <div className="text-center mt-4">
                  <Button
                    variant="link"
                    onClick={() => navigate('/')}
                    className="text-decoration-none"
                    style={{ color: '#8b4513' }}
                  >
                    <FaArrowLeft className="me-2" />
                    Back to Home
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default Login;
