// src/components/Anavbar.jsx

import React from 'react';
import { Navbar, Nav, Container, NavDropdown } from 'react-bootstrap';
import { Link, useNavigate } from "react-router-dom";

const Anavbar = () => {
  const navigate = useNavigate();
  const user = localStorage.getItem('user');
  let userName = '';
  if (user) {
    try {
      userName = JSON.parse(user).name;
    } catch (e) {
      userName = '';
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/best-wishes');
  };

  return (
    <Navbar bg="primary" variant="dark" expand="lg">
      <Container>
        <Navbar.Brand>
          <Link to='/admin/dashboard' style={{ color: "white", textDecoration: "none" }}>
            BookStore <span style={{ fontSize: "0.8em" }}>(Admin)</span>
          </Link>
        </Navbar.Brand>
        <Navbar.Toggle aria-controls="admin-navbar-nav" />
        <Navbar.Collapse id="admin-navbar-nav">
          <Nav className="ms-auto">
            <Link to="/admin/dashboard" style={{ padding: "10px", color: "white", textDecoration: "none" }}>Dashboard</Link>
            <Link to="/admin/books" style={{ padding: "10px", color: "white", textDecoration: "none" }}>Books</Link>
            <Link to="/admin/users" style={{ padding: "10px", color: "white", textDecoration: "none" }}>Users</Link>
            <Link to="/admin/sellers" style={{ padding: "10px", color: "white", textDecoration: "none" }}>Sellers</Link>
            <NavDropdown title={userName ? userName : "Admin"} id="admin-nav-dropdown" menuVariant="dark">
              <NavDropdown.Item onClick={handleLogout}>Logout</NavDropdown.Item>
            </NavDropdown>
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Anavbar;
