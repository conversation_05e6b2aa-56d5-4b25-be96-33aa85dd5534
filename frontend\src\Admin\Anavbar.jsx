// src/components/Anavbar.jsx

import React from 'react';
import { Navbar, Nav, Container, NavDropdown } from 'react-bootstrap';
import { Link, useNavigate } from "react-router-dom";

const Anavbar = () => {
  const navigate = useNavigate();
  const user = localStorage.getItem('user');
  let userName = '';
  if (user) {
    try {
      userName = JSON.parse(user).name;
    } catch (e) {
      userName = '';
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/best-wishes');
  };

  return (
    <Navbar expand="lg" style={{
      background: 'linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%)',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      padding: '0.75rem 0'
    }}>
      <Container>
        <Navbar.Brand>
          <Link to='/ahome' style={{
            color: "white",
            textDecoration: "none",
            fontWeight: "700",
            fontSize: "1.5rem",
            fontFamily: 'Segoe UI, sans-serif',
            display: 'flex',
            alignItems: 'center'
          }}>
            <img
              src="/bookstore-logo.png"
              alt="BookStore Logo"
              style={{ width: "40px", marginRight: "12px", verticalAlign: "middle" }}
              onError={e => { e.target.style.display = 'none'; }}
            />
            🛡️ BookNest
            <span style={{
              fontSize: "0.9rem",
              fontWeight: "500",
              marginLeft: "8px",
              background: 'rgba(255,255,255,0.2)',
              padding: '4px 8px',
              borderRadius: '12px'
            }}>
              Admin
            </span>
          </Link>
        </Navbar.Brand>
        <Navbar.Toggle aria-controls="admin-navbar-nav" style={{ border: 'none' }} />
        <Navbar.Collapse id="admin-navbar-nav">
          <Nav className="ms-auto" style={{ alignItems: "center" }}>
            <Link to="/ahome" style={{
              padding: "12px 16px",
              color: "white",
              textDecoration: "none",
              fontFamily: 'Segoe UI, sans-serif',
              fontWeight: '600',
              borderRadius: '8px',
              transition: 'all 0.2s ease-in-out',
              margin: '0 4px'
            }}
            onMouseEnter={(e) => e.target.style.background = 'rgba(255,255,255,0.2)'}
            onMouseLeave={(e) => e.target.style.background = 'transparent'}>
              📊 Dashboard
            </Link>
            <Link to="/items" style={{
              padding: "12px 16px",
              color: "white",
              textDecoration: "none",
              fontFamily: 'Segoe UI, sans-serif',
              fontWeight: '600',
              borderRadius: '8px',
              transition: 'all 0.2s ease-in-out',
              margin: '0 4px'
            }}
            onMouseEnter={(e) => e.target.style.background = 'rgba(255,255,255,0.2)'}
            onMouseLeave={(e) => e.target.style.background = 'transparent'}>
              📚 Books
            </Link>
            <Link to="/users" style={{
              padding: "12px 16px",
              color: "white",
              textDecoration: "none",
              fontFamily: 'Segoe UI, sans-serif',
              fontWeight: '600',
              borderRadius: '8px',
              transition: 'all 0.2s ease-in-out',
              margin: '0 4px'
            }}
            onMouseEnter={(e) => e.target.style.background = 'rgba(255,255,255,0.2)'}
            onMouseLeave={(e) => e.target.style.background = 'transparent'}>
              👥 Users
            </Link>
            <Link to="/seller" style={{
              padding: "12px 16px",
              color: "white",
              textDecoration: "none",
              fontFamily: 'Segoe UI, sans-serif',
              fontWeight: '600',
              borderRadius: '8px',
              transition: 'all 0.2s ease-in-out',
              margin: '0 4px'
            }}
            onMouseEnter={(e) => e.target.style.background = 'rgba(255,255,255,0.2)'}
            onMouseLeave={(e) => e.target.style.background = 'transparent'}>
              🏪 Sellers
            </Link>
            <NavDropdown
              title={
                <span style={{
                  color: 'white',
                  fontFamily: 'Segoe UI, sans-serif',
                  fontWeight: '600',
                  background: 'rgba(255,255,255,0.2)',
                  padding: '6px 12px',
                  borderRadius: '20px',
                  fontSize: '0.9rem'
                }}>
                  👤 {userName || "Admin"}
                </span>
              }
              id="admin-nav-dropdown"
              menuVariant="light"
              style={{ border: 'none' }}
            >
              <NavDropdown.Item
                onClick={handleLogout}
                style={{
                  fontFamily: 'Segoe UI, sans-serif',
                  fontWeight: '600',
                  color: '#6f42c1'
                }}
              >
                🚪 Logout
              </NavDropdown.Item>
            </NavDropdown>
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Anavbar;
