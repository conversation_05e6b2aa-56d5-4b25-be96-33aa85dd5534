/* Main Welcome Page Styles */
.welcome-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.welcome-navbar {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 16px 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.welcome-navbar.scrolled {
  background: rgba(255, 255, 255, 0.95) !important;
}

.welcome-navbar .navbar-brand {
  font-weight: 700;
  font-size: 28px;
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome-navbar.scrolled .navbar-brand {
  color: #2d3748 !important;
  text-shadow: none;
}

.welcome-navbar .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  margin: 0 12px;
  padding: 10px 20px !important;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
}

.welcome-navbar.scrolled .nav-link {
  color: #4a5568 !important;
}

.welcome-navbar .nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white !important;
  transform: translateY(-2px);
}

.welcome-navbar.scrolled .nav-link:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea !important;
}

.hero-section {
  padding: 120px 20px 80px;
  text-align: center;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%),
              url("https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3") center/cover;
  color: white;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 900px;
  margin: 0 auto;
}

.hero-title {
  font-size: 56px;
  font-weight: 700;
  margin-bottom: 24px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 24px;
  margin-bottom: 40px;
  opacity: 0.9;
  font-weight: 300;
  line-height: 1.4;
}

.hero-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 60px;
}

.hero-btn {
  padding: 16px 32px;
  border: none;
  border-radius: 50px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  min-width: 180px;
  justify-content: center;
}

.hero-btn.primary {
  background: white;
  color: #667eea;
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.hero-btn.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 255, 255, 0.3);
  color: #5a67d8;
  text-decoration: none;
}

.hero-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.hero-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-3px);
  color: white;
  text-decoration: none;
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.hero-btn.tertiary {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
}

.hero-btn.tertiary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(240, 147, 251, 0.4);
  color: white;
  text-decoration: none;
}

.features-section {
  padding: 80px 20px;
  background: white;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
}

.features-header {
  text-align: center;
  margin-bottom: 60px;
}

.features-title {
  font-size: 42px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 16px;
}

.features-subtitle {
  font-size: 20px;
  color: #718096;
  font-weight: 300;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.feature-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  margin: 0 auto 24px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.feature-title {
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
}

.feature-description {
  font-size: 16px;
  color: #718096;
  line-height: 1.6;
}

.cta-section {
  padding: 80px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
  text-align: center;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: 36px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 16px;
}

.cta-description {
  font-size: 18px;
  color: #718096;
  margin-bottom: 40px;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-btn {
  padding: 14px 28px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.cta-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.cta-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  color: white;
  text-decoration: none;
}

.cta-btn.secondary {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
}

.cta-btn.secondary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 42px;
  }
  
  .hero-subtitle {
    font-size: 20px;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-btn {
    width: 100%;
    max-width: 300px;
  }
  
  .features-title {
    font-size: 32px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .feature-card {
    padding: 30px;
  }
  
  .cta-title {
    font-size: 28px;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 100px 15px 60px;
  }
  
  .hero-title {
    font-size: 32px;
  }
  
  .hero-subtitle {
    font-size: 18px;
  }
  
  .features-section {
    padding: 60px 15px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-card {
    padding: 24px;
  }
  
  .cta-section {
    padding: 60px 15px;
  }
}
